import pandas as pd
import numpy as np
import re
from datetime import datetime

def limpar_cpf_cnpj(valor):
    """Limpa CPF/CNPJ removendo caracteres especiais"""
    if pd.isna(valor):
        return None
    
    valor_str = str(valor).strip()
    if valor_str in ['nan', 'None', '']:
        return None
    
    # Remove caracteres especiais
    valor_limpo = re.sub(r'[^\d]', '', valor_str)
    
    # Verifica se tem tamanho válido
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def carregar_dados():
    """Carrega todos os dados necessários"""
    print("CRIANDO ARQUIVO FINAL COM AGENDOR E FLAG LEGALONE")
    print("="*60)
    print("Carregando dados...")
    
    # Carregar boletos
    print("- Carregando boletos...")
    df_boletos = pd.read_excel("data/boletos_legalone.xlsx")
    print(f"  ✓ {len(df_boletos)} registros de boletos")
    
    # Carregar bases de clientes
    print("- Carregando base PF...")
    df_pf = pd.read_excel("data/base_pf_legalone.xlsx")
    print(f"  ✓ {len(df_pf)} registros PF")
    
    print("- Carregando base PJ...")
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    print(f"  ✓ {len(df_pj)} registros PJ")
    
    # Carregar CNAEs
    print("- Carregando CNAEs...")
    df_cnaes = pd.read_excel("data/cnaes.xlsx")
    print(f"  ✓ {len(df_cnaes)} registros de CNAE")
    
    # Carregar database Agendor
    print("- Carregando database Agendor...")
    try:
        df_agendor = pd.read_excel("data/database_agendor.xlsx")
        print(f"  ✓ {len(df_agendor)} registros do Agendor")
    except Exception as e:
        print(f"  ⚠️ Erro ao carregar Agendor: {e}")
        df_agendor = pd.DataFrame()
    
    return df_boletos, df_pf, df_pj, df_cnaes, df_agendor

def processar_boletos(df_boletos):
    """Processa dados dos boletos"""
    print("\nProcessando boletos...")
    
    # Renomear colunas para padronizar
    colunas_boletos = {
        'Movimentos / Recebido em': 'data_recebimento',
        'Valor líquido realizado': 'valor_liquido',
        'Classificações / Destinatário da fatura / Nome/razão social': 'nome_destinatario',
        'Classificações / Destinatário da fatura / CPF/CNPJ': 'cpf_cnpj_original'
    }
    
    df_boletos_renamed = df_boletos.rename(columns=colunas_boletos)
    
    # Limpar CPF/CNPJ
    df_boletos_renamed['cpf_cnpj_limpo'] = df_boletos_renamed['cpf_cnpj_original'].apply(limpar_cpf_cnpj)
    
    # Filtrar apenas registros com CPF/CNPJ válido
    df_boletos_validos = df_boletos_renamed[df_boletos_renamed['cpf_cnpj_limpo'].notna()].copy()
    
    # Converter data
    df_boletos_validos['data_recebimento'] = pd.to_datetime(df_boletos_validos['data_recebimento'])
    
    # Converter valor para numérico
    df_boletos_validos['valor_liquido'] = pd.to_numeric(df_boletos_validos['valor_liquido'], errors='coerce')
    
    print(f"  ✓ {len(df_boletos_validos)} boletos com CPF/CNPJ válido processados")
    
    return df_boletos_validos

def processar_bases_clientes(df_pf, df_pj):
    """Processa bases de clientes PF e PJ"""
    print("\nProcessando bases de clientes...")
    
    # Processar PF
    df_pf_processado = df_pf.copy()
    df_pf_processado['cpf_cnpj_limpo'] = df_pf_processado['CPF'].apply(limpar_cpf_cnpj)
    df_pf_processado['tipo'] = 'PF'
    df_pf_processado['nome'] = df_pf_processado['Nome']
    df_pf_processado['telefone_principal'] = df_pf_processado.get('Telefones / Principal', None)
    df_pf_processado['telefone_numero'] = df_pf_processado.get('Telefones / Número', None)
    df_pf_processado['email'] = df_pf_processado.get('E-mails / E-mail', None)
    df_pf_processado['uf'] = df_pf_processado.get('Endereços / UF', None)
    df_pf_processado['cnae'] = None
    df_pf_processado['regime_tributario'] = None
    
    # Processar PJ - CAPTURAR TODOS OS CNAEs
    df_pj_processado = df_pj.copy()
    df_pj_processado['cpf_cnpj_limpo'] = df_pj_processado['CNPJ'].apply(limpar_cpf_cnpj)
    df_pj_processado['tipo'] = 'PJ'
    df_pj_processado['nome'] = df_pj_processado['Razão social']
    df_pj_processado['telefone_principal'] = df_pj_processado.get('Telefones / Principal', None)
    df_pj_processado['telefone_numero'] = df_pj_processado.get('Telefones / Número', None)
    df_pj_processado['email'] = df_pj_processado.get('E-mails / E-mail', None)
    df_pj_processado['uf'] = df_pj_processado.get('Endereços / UF', None)
    df_pj_processado['cnae'] = df_pj_processado.get('CNAEs / Código', None)
    df_pj_processado['regime_tributario'] = df_pj_processado.get('Regime Tributário', None)
    
    # Selecionar colunas relevantes
    colunas_finais = ['cpf_cnpj_limpo', 'tipo', 'nome', 'telefone_principal', 'telefone_numero', 
                     'email', 'uf', 'cnae', 'regime_tributario']
    
    df_pf_final = df_pf_processado[colunas_finais]
    df_pj_final = df_pj_processado[colunas_finais]
    
    # Combinar - NÃO remover duplicatas para capturar todos os CNAEs
    df_clientes = pd.concat([df_pf_final, df_pj_final], ignore_index=True)
    df_clientes = df_clientes[df_clientes['cpf_cnpj_limpo'].notna()]
    
    print(f"  ✓ {len(df_clientes)} registros de clientes processados (incluindo múltiplos CNAEs)")
    
    cnpjs_duplicados = df_clientes[df_clientes['cpf_cnpj_limpo'].duplicated(keep=False)]['cpf_cnpj_limpo'].nunique()
    print(f"  ✓ {cnpjs_duplicados} CNPJs com múltiplos registros (múltiplos CNAEs)")
    
    return df_clientes

def processar_agendor(df_agendor):
    """Processa dados do Agendor para busca complementar"""
    if df_agendor.empty:
        print("\n  ⚠️ Database Agendor vazio - pulando processamento")
        return pd.DataFrame()
    
    print("\nProcessando database Agendor...")
    
    # Limpar CPF/CNPJ
    df_agendor['cpf_limpo'] = df_agendor['CPF'].apply(limpar_cpf_cnpj)
    df_agendor['cnpj_limpo'] = df_agendor['CNPJ'].apply(limpar_cpf_cnpj)
    df_agendor['cpf_cnpj_limpo'] = df_agendor['cpf_limpo'].fillna(df_agendor['cnpj_limpo'])
    
    # Filtrar apenas registros com CPF/CNPJ válido
    df_agendor_valido = df_agendor[df_agendor['cpf_cnpj_limpo'].notna()].copy()
    
    # Consolidar dados por CPF/CNPJ
    agendor_consolidado = []
    
    for cpf_cnpj, grupo in df_agendor_valido.groupby('cpf_cnpj_limpo'):
        # Nome (priorizar empresa)
        nome_empresa = grupo['Empresa relacionada'].dropna().iloc[0] if not grupo['Empresa relacionada'].dropna().empty else None
        nome_pessoa = grupo['Pessoa relacionada'].dropna().iloc[0] if not grupo['Pessoa relacionada'].dropna().empty else None
        nome = nome_empresa if nome_empresa else nome_pessoa
        
        # Consolidar contatos
        emails = grupo['E-mail'].dropna().unique()
        email_str = '; '.join([str(e).strip() for e in emails if str(e).strip() not in ['nan', 'None', ''] and '@' in str(e)]) if len(emails) > 0 else None
        
        telefones_lista = []
        for col in ['Telefone', 'Celular', 'WhatsApp']:
            if col in grupo.columns:
                tels = grupo[col].dropna().unique()
                for tel in tels:
                    tel_str = str(tel).strip()
                    if tel_str not in ['nan', 'None', ''] and len(tel_str) > 3:
                        telefones_lista.append(tel_str)
        
        telefones_str = '; '.join(list(set(telefones_lista))) if telefones_lista else None
        
        # Outros dados
        uf = grupo['Estado'].dropna().iloc[0] if not grupo['Estado'].dropna().empty else None
        tipo = 'PJ' if len(cpf_cnpj) == 14 else 'PF'
        
        agendor_consolidado.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome_cliente': nome,
            'tipo_destinatario': tipo,
            'telefones': telefones_str,
            'emails': email_str,
            'uf': uf
        })
    
    df_agendor_final = pd.DataFrame(agendor_consolidado)
    print(f"  ✓ {len(df_agendor_final)} clientes únicos processados do Agendor")
    
    return df_agendor_final

def consolidar_dados_cliente(df_clientes):
    """Consolida dados de clientes capturando TODOS os CNAEs"""
    print("\nConsolidando dados de clientes (capturando TODOS os CNAEs)...")
    
    clientes_consolidados = []
    
    for cpf_cnpj, grupo in df_clientes.groupby('cpf_cnpj_limpo'):
        # Dados básicos
        nome = grupo['nome'].dropna().iloc[0] if not grupo['nome'].dropna().empty else None
        tipo = grupo['tipo'].dropna().iloc[0] if not grupo['tipo'].dropna().empty else None
        uf = grupo['uf'].dropna().iloc[0] if not grupo['uf'].dropna().empty else None
        regime_tributario = grupo['regime_tributario'].dropna().iloc[0] if not grupo['regime_tributario'].dropna().empty else None
        
        # Consolidar telefones
        telefones_lista = []
        for col in ['telefone_principal', 'telefone_numero']:
            telefones = grupo[col].dropna().unique()
            for tel in telefones:
                tel_str = str(tel).strip()
                if tel_str not in ['nan', 'None', 'Sim', 'Não', ''] and len(tel_str) > 3:
                    telefones_lista.append(tel_str)
        
        telefones_str = '; '.join(list(set(telefones_lista))) if telefones_lista else None
        
        # Consolidar emails
        emails = grupo['email'].dropna().unique()
        emails_validos = []
        for email in emails:
            email_str = str(email).strip()
            if email_str not in ['nan', 'None', 'Sim', 'Não', ''] and '@' in email_str:
                emails_validos.append(email_str)
        
        emails_str = '; '.join(list(set(emails_validos))) if emails_validos else None
        
        # CONSOLIDAR TODOS OS CNAEs
        cnaes = grupo['cnae'].dropna().unique()
        cnae_principal = None
        cnaes_str = None
        
        if len(cnaes) > 0:
            cnaes_validos = []
            for cnae in cnaes:
                cnae_str = str(cnae).strip()
                if cnae_str not in ['nan', 'None', '']:
                    cnaes_validos.append(cnae_str)
            
            if cnaes_validos:
                cnaes_unicos = sorted(list(set(cnaes_validos)))
                cnae_principal = cnaes_unicos[0]
                cnaes_str = '; '.join(cnaes_unicos)
        
        clientes_consolidados.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome_cliente': nome,
            'tipo_destinatario': tipo,
            'uf': uf,
            'telefones': telefones_str,
            'emails': emails_str,
            'cnae': cnaes_str,
            'primeiro_cnae': cnae_principal,
            'regime_tributario': regime_tributario
        })
    
    df_consolidado = pd.DataFrame(clientes_consolidados)
    print(f"  ✓ {len(df_consolidado)} clientes consolidados")
    
    clientes_com_multiplos_cnaes = df_consolidado[df_consolidado['cnae'].str.contains(';', na=False)]
    print(f"  ✓ {len(clientes_com_multiplos_cnaes)} clientes com múltiplos CNAEs")
    
    return df_consolidado

def calcular_metricas_com_agendor(df_boletos_processados, df_clientes_consolidados, df_agendor_processado):
    """Calcula métricas dos boletos com busca complementar no Agendor e flag LegalOne"""
    print("\nCalculando métricas dos boletos com busca complementar...")

    metricas_lista = []
    clientes_nao_encontrados = 0
    clientes_encontrados_agendor = 0

    for cpf_cnpj, grupo_boletos in df_boletos_processados.groupby('cpf_cnpj_limpo'):
        # Buscar dados do cliente no LegalOne
        cliente_info = df_clientes_consolidados[df_clientes_consolidados['cpf_cnpj_limpo'] == cpf_cnpj]

        if len(cliente_info) == 0:
            # ✅ CLIENTE NÃO ENCONTRADO NO LEGALONE
            clientes_nao_encontrados += 1
            cliente_legalone = 'NAO'

            # Buscar no Agendor
            agendor_info = df_agendor_processado[df_agendor_processado['cpf_cnpj_limpo'] == cpf_cnpj]

            if len(agendor_info) > 0:
                # Encontrado no Agendor
                clientes_encontrados_agendor += 1
                agendor_data = agendor_info.iloc[0]

                cliente_data = {
                    'nome_cliente': agendor_data['nome_cliente'],
                    'tipo_destinatario': agendor_data['tipo_destinatario'],
                    'uf': agendor_data['uf'],
                    'telefones': agendor_data['telefones'],
                    'emails': agendor_data['emails'],
                    'cnae': None,
                    'primeiro_cnae': None,
                    'regime_tributario': None
                }
            else:
                # Não encontrado em lugar nenhum
                nome_cliente = grupo_boletos['nome_destinatario'].iloc[0]
                tipo_destinatario = 'PJ' if len(cpf_cnpj) == 14 else 'PF'

                cliente_data = {
                    'nome_cliente': nome_cliente,
                    'tipo_destinatario': tipo_destinatario,
                    'uf': None,
                    'telefones': None,
                    'emails': None,
                    'cnae': None,
                    'primeiro_cnae': None,
                    'regime_tributario': None
                }
        else:
            # ✅ CLIENTE ENCONTRADO NO LEGALONE
            cliente_legalone = 'SIM'
            cliente_data = cliente_info.iloc[0].to_dict()

        # Calcular métricas dos boletos
        valores = grupo_boletos['valor_liquido'].astype(float)
        datas = pd.to_datetime(grupo_boletos['data_recebimento'])

        total_boletos = len(grupo_boletos)
        valor_total = valores.sum()
        valor_medio = valores.mean()
        valor_min = valores.min()
        valor_max = valores.max()

        data_primeiro = datas.min()
        data_ultimo = datas.max()

        # Período em meses (incluindo primeiro mês)
        if data_primeiro == data_ultimo:
            periodo_meses = 1
        else:
            periodo_meses = ((data_ultimo.year - data_primeiro.year) * 12 +
                           (data_ultimo.month - data_primeiro.month)) + 1

        # Meses desde último boleto
        data_hoje = pd.Timestamp.now()
        meses_desde_ultimo = ((data_hoje.year - data_ultimo.year) * 12 +
                             (data_hoje.month - data_ultimo.month))

        # Frequência
        frequencia = total_boletos / periodo_meses if periodo_meses > 0 else 0

        metricas_lista.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome_cliente': cliente_data['nome_cliente'],
            'tipo_destinatario': cliente_data['tipo_destinatario'],
            'cliente_legalone': cliente_legalone,  # ✅ NOVA COLUNA
            'meses_desde_ultimo_boleto': meses_desde_ultimo,
            'data_ultimo_boleto': data_ultimo,
            'data_primeiro_boleto': data_primeiro,
            'periodo_cliente_meses': periodo_meses,
            'total_boletos_unicos': total_boletos,
            'valor_total_recebido': valor_total,
            'valor_medio_por_boleto': valor_medio,
            'valor_minimo': valor_min,
            'valor_maximo': valor_max,
            'frequencia_boletos_por_mes': frequencia,
            'telefones': cliente_data['telefones'],
            'emails': cliente_data['emails'],
            'uf': cliente_data['uf'],
            'cnae': cliente_data['cnae'],
            'regime_tributario': cliente_data['regime_tributario'],
            'primeiro_cnae': cliente_data['primeiro_cnae']
        })

    df_metricas = pd.DataFrame(metricas_lista)
    print(f"  ✓ Métricas calculadas para {len(df_metricas)} clientes")
    print(f"  ✓ {len(df_metricas[df_metricas['cliente_legalone'] == 'SIM'])} clientes encontrados no LegalOne")
    print(f"  ✓ {clientes_nao_encontrados} clientes NÃO encontrados no LegalOne")
    print(f"  ✓ {clientes_encontrados_agendor} clientes complementados com dados do Agendor")

    return df_metricas

def adicionar_descricao_cnae(df_final, df_cnaes):
    """Adiciona descrição do CNAE baseado no primeiro_cnae"""
    print("\nAdicionando descrições de CNAE...")

    # Preparar dicionário de CNAEs
    cnae_dict = {}
    for _, row in df_cnaes.iterrows():
        cnae_codigo = str(row['CNAE']).strip()
        cnae_descricao = str(row['DESCRIÇÃO']).strip()
        cnae_dict[cnae_codigo] = cnae_descricao

    def buscar_descricao_cnae(cnae_codigo):
        if pd.isna(cnae_codigo) or str(cnae_codigo).strip() in ['nan', 'None', '']:
            return None

        cnae_str = str(cnae_codigo).strip()

        # Busca exata
        if cnae_str in cnae_dict:
            return cnae_dict[cnae_str]

        # Busca sem formatação
        cnae_limpo = re.sub(r'[^\d]', '', cnae_str)
        for codigo, descricao in cnae_dict.items():
            codigo_limpo = re.sub(r'[^\d]', '', codigo)
            if cnae_limpo == codigo_limpo:
                return descricao

        return None

    df_final['Descricao_CNAE'] = df_final['primeiro_cnae'].apply(buscar_descricao_cnae)

    total_cnaes = df_final['primeiro_cnae'].notna().sum()
    cnaes_com_descricao = df_final['Descricao_CNAE'].notna().sum()

    print(f"  ✓ {cnaes_com_descricao}/{total_cnaes} CNAEs com descrição encontrada ({cnaes_com_descricao/total_cnaes*100:.1f}%)")

    return df_final

def formatar_arquivo_final(df_metricas):
    """Formata o arquivo final com nomes corretos e datas DD/MM/AAAA"""
    print("\nFormatando arquivo final...")

    # Renomear colunas para nomes corretos de BOLETOS
    colunas_finais = {
        'nome_cliente': 'Nome',
        'cpf_cnpj_limpo': 'CPF_CNPJ',
        'tipo_destinatario': 'Tipo_Destinatario',
        'cliente_legalone': 'Cliente_LegalOne',  # ✅ NOVA COLUNA
        'meses_desde_ultimo_boleto': 'Meses_Desde_Ultimo_Boleto',
        'data_ultimo_boleto': 'Data_Ultimo_Boleto',
        'data_primeiro_boleto': 'Data_Primeiro_Boleto',
        'periodo_cliente_meses': 'Periodo_Cliente_Meses',
        'total_boletos_unicos': 'Total_Boletos_Unicos',
        'valor_total_recebido': 'Valor_Total_Recebido',
        'valor_medio_por_boleto': 'Valor_Medio_Por_Boleto',
        'valor_minimo': 'Valor_Minimo',
        'valor_maximo': 'Valor_Maximo',
        'frequencia_boletos_por_mes': 'Frequencia_Boletos_Por_Mes',
        'telefones': 'Telefones',
        'emails': 'Emails',
        'uf': 'UF',
        'cnae': 'CNAE',
        'regime_tributario': 'Regime_Tributario',
        'primeiro_cnae': 'Primeiro_CNAE'
    }

    df_formatado = df_metricas.rename(columns=colunas_finais)

    # Formatar datas para DD/MM/AAAA
    print("  ✓ Formatando datas para DD/MM/AAAA...")
    colunas_data = ['Data_Ultimo_Boleto', 'Data_Primeiro_Boleto']

    for coluna in colunas_data:
        if coluna in df_formatado.columns:
            df_formatado[coluna] = pd.to_datetime(df_formatado[coluna], errors='coerce')
            df_formatado[coluna] = df_formatado[coluna].dt.strftime('%d/%m/%Y')

    # Arredondar valores numéricos para 2 casas decimais
    colunas_numericas = ['Valor_Total_Recebido', 'Valor_Medio_Por_Boleto', 'Valor_Minimo',
                        'Valor_Maximo', 'Frequencia_Boletos_Por_Mes']

    for coluna in colunas_numericas:
        if coluna in df_formatado.columns:
            df_formatado[coluna] = df_formatado[coluna].round(2)

    print(f"  ✓ Arquivo formatado com {len(df_formatado)} registros")

    return df_formatado

def main():
    # Carregar dados
    df_boletos, df_pf, df_pj, df_cnaes, df_agendor = carregar_dados()

    # Processar dados
    df_boletos_processados = processar_boletos(df_boletos)
    df_clientes = processar_bases_clientes(df_pf, df_pj)
    df_agendor_processado = processar_agendor(df_agendor)
    df_clientes_consolidados = consolidar_dados_cliente(df_clientes)

    # Calcular métricas com busca no Agendor
    df_metricas = calcular_metricas_com_agendor(df_boletos_processados, df_clientes_consolidados, df_agendor_processado)

    # Adicionar descrições de CNAE
    df_com_cnae = adicionar_descricao_cnae(df_metricas, df_cnaes)

    # Formatar arquivo final
    df_final = formatar_arquivo_final(df_com_cnae)

    # Salvar arquivo
    nome_arquivo = "teste_novo/arquivo_final_boletos_com_agendor.xlsx"
    df_final.to_excel(nome_arquivo, index=False)

    print(f"\n✅ ARQUIVO SALVO: {nome_arquivo}")

    # Estatísticas finais
    print(f"\n📊 RESUMO FINAL:")
    print(f"- Total de registros: {len(df_final)}")
    print(f"- Clientes LegalOne: {len(df_final[df_final['Cliente_LegalOne'] == 'SIM'])}")
    print(f"- Clientes NÃO LegalOne: {len(df_final[df_final['Cliente_LegalOne'] == 'NAO'])}")
    print(f"- Clientes PF: {len(df_final[df_final['Tipo_Destinatario'] == 'PF'])}")
    print(f"- Clientes PJ: {len(df_final[df_final['Tipo_Destinatario'] == 'PJ'])}")
    print(f"- Clientes com múltiplos CNAEs: {len(df_final[df_final['CNAE'].str.contains(';', na=False)])}")
    print(f"- Valor total recebido: R$ {df_final['Valor_Total_Recebido'].sum():,.2f}")

    print(f"\n🎯 MELHORIAS IMPLEMENTADAS:")
    print(f"   ✅ Flag 'Cliente_LegalOne' (SIM/NAO)")
    print(f"   ✅ Busca complementar no Agendor para clientes não encontrados")
    print(f"   ✅ TODOS os CNAEs capturados (separados por ;)")
    print(f"   ✅ Datas formatadas como DD/MM/AAAA")
    print(f"   ✅ Nomes corretos (BOLETOS, não faturas)")

if __name__ == "__main__":
    main()
