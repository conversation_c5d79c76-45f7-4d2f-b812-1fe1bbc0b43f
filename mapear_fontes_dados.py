#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para mapear todas as fontes de dados e o que é extraído de cada arquivo
"""

import pandas as pd
import os
from pathlib import Path

def analisar_arquivo(caminho, nome_arquivo):
    """
    Analisa um arquivo e retorna informações sobre suas colunas
    """
    try:
        if caminho.suffix == '.xlsx':
            df = pd.read_excel(caminho)
        elif caminho.suffix == '.csv':
            df = pd.read_csv(caminho)
        else:
            return None
            
        info = {
            'arquivo': nome_arquivo,
            'registros': len(df),
            'colunas': len(df.columns),
            'lista_colunas': list(df.columns),
            'amostra': df.head(2).to_dict('records') if len(df) > 0 else []
        }
        return info
    except Exception as e:
        return {
            'arquivo': nome_arquivo,
            'erro': str(e)
        }

def mapear_todas_fontes():
    """
    Mapeia todas as fontes de dados do projeto
    """
    print("🗺️  MAPEAMENTO COMPLETO DAS FONTES DE DADOS")
    print("="*80)
    
    pasta_data = Path('data')
    
    # Arquivos principais de entrada
    arquivos_entrada = [
        'dados_legalone_novo.xlsx',
        'faturas_emitidas.xlsx', 
        'base_pf_legalone.xlsx',
        'base_pj_legalone.xlsx',
        'cnaes.xlsx'
    ]
    
    # Arquivos processados/intermediários
    arquivos_processados = [
        'faturas_limpa.xlsx',
        'pf_limpa.xlsx', 
        'pj_limpa.xlsx',
        'metricas_destinatarios.xlsx',
        'dados_preparados_novo.xlsx'
    ]
    
    # Arquivos de saída
    arquivos_saida = [
        'clientes_consolidado_completo.xlsx',
        'metricas_clientes_novo.xlsx',
        'metricas_clientes_novo.csv',
        'clientes_ativos_12meses.xlsx',
        'clientes_ativos_12meses.csv'
    ]
    
    print("\n📥 ARQUIVOS DE ENTRADA (Dados Originais)")
    print("-" * 60)
    for arquivo in arquivos_entrada:
        caminho = pasta_data / arquivo
        if caminho.exists():
            info = analisar_arquivo(caminho, arquivo)
            if info and 'erro' not in info:
                print(f"\n✅ {arquivo}")
                print(f"   📊 {info['registros']:,} registros, {info['colunas']} colunas")
                print(f"   📋 Colunas: {', '.join(info['lista_colunas'][:5])}{'...' if len(info['lista_colunas']) > 5 else ''}")
            else:
                print(f"\n❌ {arquivo} - Erro: {info.get('erro', 'Desconhecido')}")
        else:
            print(f"\n⚠️  {arquivo} - Arquivo não encontrado")
    
    print(f"\n\n🔄 ARQUIVOS PROCESSADOS (Dados Intermediários)")
    print("-" * 60)
    for arquivo in arquivos_processados:
        caminho = pasta_data / arquivo
        if caminho.exists():
            info = analisar_arquivo(caminho, arquivo)
            if info and 'erro' not in info:
                print(f"\n✅ {arquivo}")
                print(f"   📊 {info['registros']:,} registros, {info['colunas']} colunas")
                print(f"   📋 Colunas: {', '.join(info['lista_colunas'][:5])}{'...' if len(info['lista_colunas']) > 5 else ''}")
            else:
                print(f"\n❌ {arquivo} - Erro: {info.get('erro', 'Desconhecido')}")
        else:
            print(f"\n⚠️  {arquivo} - Arquivo não encontrado")
    
    print(f"\n\n📤 ARQUIVOS DE SAÍDA (Dados Finais)")
    print("-" * 60)
    for arquivo in arquivos_saida:
        caminho = pasta_data / arquivo
        if caminho.exists():
            info = analisar_arquivo(caminho, arquivo)
            if info and 'erro' not in info:
                print(f"\n✅ {arquivo}")
                print(f"   📊 {info['registros']:,} registros, {info['colunas']} colunas")
                print(f"   📋 Colunas: {', '.join(info['lista_colunas'][:5])}{'...' if len(info['lista_colunas']) > 5 else ''}")
            else:
                print(f"\n❌ {arquivo} - Erro: {info.get('erro', 'Desconhecido')}")
        else:
            print(f"\n⚠️  {arquivo} - Arquivo não encontrado")

def detalhar_fontes_especificas():
    """
    Detalha o que é extraído de cada fonte específica
    """
    print(f"\n\n🔍 DETALHAMENTO DAS EXTRAÇÕES POR FONTE")
    print("="*80)
    
    pasta_data = Path('data')
    
    # Analisar dados_legalone_novo.xlsx (NOVO FORMATO)
    print(f"\n🆕 DADOS_LEGALONE_NOVO.XLSX (Pipeline Unificado)")
    print("-" * 60)
    arquivo_novo = pasta_data / 'dados_legalone_novo.xlsx'
    if arquivo_novo.exists():
        df_novo = pd.read_excel(arquivo_novo)
        print(f"📊 {len(df_novo):,} registros, {len(df_novo.columns)} colunas")
        print(f"📋 TODAS as colunas extraídas:")
        for i, col in enumerate(df_novo.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # Mostrar amostra de dados
        print(f"\n📝 Amostra dos dados (primeiras 2 linhas):")
        amostra = df_novo.head(2)
        for col in df_novo.columns[:8]:  # Mostrar apenas primeiras 8 colunas
            print(f"   {col}: {list(amostra[col])}")
    else:
        print("❌ Arquivo não encontrado")
    
    # Analisar base_pf_legalone.xlsx
    print(f"\n👤 BASE_PF_LEGALONE.XLSX (Pessoas Físicas)")
    print("-" * 60)
    arquivo_pf = pasta_data / 'base_pf_legalone.xlsx'
    if arquivo_pf.exists():
        df_pf = pd.read_excel(arquivo_pf)
        print(f"📊 {len(df_pf):,} registros, {len(df_pf.columns)} colunas")
        print(f"📋 Colunas extraídas:")
        for i, col in enumerate(df_pf.columns, 1):
            print(f"   {i:2d}. {col}")
        print(f"\n💡 Usado para: Identificar clientes PF, obter CPF, telefones, emails")
    else:
        print("❌ Arquivo não encontrado")
    
    # Analisar base_pj_legalone.xlsx  
    print(f"\n🏢 BASE_PJ_LEGALONE.XLSX (Pessoas Jurídicas)")
    print("-" * 60)
    arquivo_pj = pasta_data / 'base_pj_legalone.xlsx'
    if arquivo_pj.exists():
        df_pj = pd.read_excel(arquivo_pj)
        print(f"📊 {len(df_pj):,} registros, {len(df_pj.columns)} colunas")
        print(f"📋 Colunas extraídas:")
        for i, col in enumerate(df_pj.columns, 1):
            print(f"   {i:2d}. {col}")
        print(f"\n💡 Usado para: Identificar clientes PJ, obter CNPJ, CNAE, regime tributário, UF")
    else:
        print("❌ Arquivo não encontrado")
    
    # Analisar faturas_emitidas.xlsx
    print(f"\n📄 FATURAS_EMITIDAS.XLSX (Movimentações Financeiras)")
    print("-" * 60)
    arquivo_faturas = pasta_data / 'faturas_emitidas.xlsx'
    if arquivo_faturas.exists():
        df_faturas = pd.read_excel(arquivo_faturas)
        print(f"📊 {len(df_faturas):,} registros, {len(df_faturas.columns)} colunas")
        print(f"📋 Colunas extraídas:")
        for i, col in enumerate(df_faturas.columns, 1):
            print(f"   {i:2d}. {col}")
        print(f"\n💡 Usado para: Calcular métricas financeiras, datas, valores, frequência")
    else:
        print("❌ Arquivo não encontrado")

if __name__ == "__main__":
    mapear_todas_fontes()
    detalhar_fontes_especificas()
    
    print(f"\n\n📋 RESUMO DO FLUXO DE DADOS:")
    print("="*80)
    print("1. 📥 ENTRADA:")
    print("   • dados_legalone_novo.xlsx → Pipeline Unificado (NOVO)")
    print("   • faturas_emitidas.xlsx → Cálculos financeiros (ANTIGO)")
    print("   • base_pf_legalone.xlsx → Dados de PF (ANTIGO)")
    print("   • base_pj_legalone.xlsx → Dados de PJ (ANTIGO)")
    print()
    print("2. 🔄 PROCESSAMENTO:")
    print("   • limpar_dados.py → Limpa e normaliza dados")
    print("   • calcular_metricas.py → Calcula métricas por cliente")
    print("   • consolidar_dados_completos.py → Consolida tudo")
    print("   • pipeline_unificado.py → Processa dados novos")
    print()
    print("3. 📤 SAÍDA:")
    print("   • metricas_clientes_novo.xlsx → Dados do pipeline novo")
    print("   • clientes_consolidado_completo.xlsx → Dados do formato antigo")
    print("   • app_streamlit.py → Dashboard interativo")
