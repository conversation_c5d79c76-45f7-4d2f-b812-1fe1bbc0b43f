import pandas as pd
import numpy as np
from datetime import datetime
import os

def carregar_dados():
    """Carrega os dados dos arquivos financeiros"""
    print("Carregando dados dos arquivos financeiros...")
    
    # Carregar boletos
    try:
        boletos = pd.read_excel('../data/boletos_legalone.xlsx')
        print(f"✓ Boletos carregados: {len(boletos)} registros")
    except Exception as e:
        print(f"✗ Erro ao carregar boletos: {e}")
        return None, None
    
    # Carregar faturas
    try:
        faturas = pd.read_excel('../data/faturas_emitidas.xlsx')
        print(f"✓ Faturas carregadas: {len(faturas)} registros")
    except Exception as e:
        print(f"✗ Erro ao carregar faturas: {e}")
        return boletos, None
    
    return boletos, faturas

def analisar_estrutura(df, nome_arquivo):
    """Analisa a estrutura de um DataFrame"""
    print(f"\n{'='*50}")
    print(f"ESTRUTURA DO ARQUIVO: {nome_arquivo}")
    print(f"{'='*50}")
    
    print(f"Dimensões: {df.shape[0]} linhas x {df.shape[1]} colunas")
    print(f"\nColunas disponíveis:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print(f"\nPrimeiras 3 linhas:")
    print(df.head(3).to_string())
    
    print(f"\nTipos de dados:")
    print(df.dtypes.to_string())
    
    return df

def identificar_campo_cpf_cnpj(df, nome_arquivo):
    """Identifica qual campo contém CPF/CNPJ"""
    print(f"\n{'='*30}")
    print(f"IDENTIFICANDO CPF/CNPJ em {nome_arquivo}")
    print(f"{'='*30}")
    
    campos_possiveis = []
    
    for col in df.columns:
        col_lower = col.lower()
        if any(termo in col_lower for termo in ['cpf', 'cnpj', 'documento', 'doc']):
            campos_possiveis.append(col)
            print(f"Campo candidato: {col}")
            
            # Mostrar alguns valores de exemplo
            valores_unicos = df[col].dropna().unique()[:10]
            print(f"  Exemplos: {valores_unicos}")
            print(f"  Total únicos: {df[col].nunique()}")
            print(f"  Valores nulos: {df[col].isnull().sum()}")
    
    return campos_possiveis

def limpar_cpf_cnpj(valor):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(valor):
        return None
    
    # Converter para string e remover caracteres especiais
    valor_str = str(valor).strip()
    valor_limpo = ''.join(filter(str.isdigit, valor_str))
    
    # Retornar apenas se tiver tamanho válido (11 para CPF, 14 para CNPJ)
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def analisar_clientes_unicos(df, campo_cpf_cnpj, nome_arquivo):
    """Analisa clientes únicos baseado no CPF/CNPJ"""
    print(f"\n{'='*40}")
    print(f"ANÁLISE DE CLIENTES ÚNICOS - {nome_arquivo}")
    print(f"{'='*40}")
    
    if campo_cpf_cnpj not in df.columns:
        print(f"✗ Campo {campo_cpf_cnpj} não encontrado!")
        return None
    
    # Limpar CPF/CNPJ
    df_clean = df.copy()
    df_clean['cpf_cnpj_limpo'] = df_clean[campo_cpf_cnpj].apply(limpar_cpf_cnpj)
    
    # Remover valores nulos
    df_validos = df_clean.dropna(subset=['cpf_cnpj_limpo'])
    
    print(f"Total de registros: {len(df)}")
    print(f"Registros com CPF/CNPJ válido: {len(df_validos)}")
    print(f"Registros sem CPF/CNPJ válido: {len(df) - len(df_validos)}")
    
    # Clientes únicos
    clientes_unicos = df_validos['cpf_cnpj_limpo'].nunique()
    print(f"Clientes únicos (CPF/CNPJ): {clientes_unicos}")
    
    # Análise de duplicatas
    duplicatas = df_validos.groupby('cpf_cnpj_limpo').size()
    clientes_com_multiplos_registros = (duplicatas > 1).sum()
    
    print(f"Clientes com múltiplos registros: {clientes_com_multiplos_registros}")
    
    if clientes_com_multiplos_registros > 0:
        print(f"\nTop 10 clientes com mais registros:")
        top_duplicatas = duplicatas.sort_values(ascending=False).head(10)
        for cpf_cnpj, count in top_duplicatas.items():
            print(f"  {cpf_cnpj}: {count} registros")
    
    return df_validos

def analisar_metricas_financeiras(df, nome_arquivo):
    """Analisa métricas financeiras"""
    print(f"\n{'='*40}")
    print(f"MÉTRICAS FINANCEIRAS - {nome_arquivo}")
    print(f"{'='*40}")
    
    # Identificar campos de valor
    campos_valor = []
    for col in df.columns:
        col_lower = col.lower()
        if any(termo in col_lower for termo in ['valor', 'preco', 'total', 'amount']):
            if df[col].dtype in ['float64', 'int64']:
                campos_valor.append(col)
    
    print(f"Campos de valor identificados: {campos_valor}")
    
    for campo in campos_valor:
        valores = df[campo].dropna()
        if len(valores) > 0:
            print(f"\n{campo}:")
            print(f"  Total: R$ {valores.sum():,.2f}")
            print(f"  Média: R$ {valores.mean():,.2f}")
            print(f"  Mediana: R$ {valores.median():,.2f}")
            print(f"  Mínimo: R$ {valores.min():,.2f}")
            print(f"  Máximo: R$ {valores.max():,.2f}")
    
    # Identificar campos de data
    campos_data = []
    for col in df.columns:
        col_lower = col.lower()
        if any(termo in col_lower for termo in ['data', 'date', 'vencimento', 'emissao']):
            campos_data.append(col)
    
    print(f"\nCampos de data identificados: {campos_data}")
    
    return campos_valor, campos_data

def comparar_bases(boletos_validos, faturas_validas):
    """Compara as duas bases de dados"""
    print(f"\n{'='*50}")
    print(f"COMPARAÇÃO ENTRE BOLETOS E FATURAS")
    print(f"{'='*50}")
    
    if boletos_validos is None or faturas_validas is None:
        print("✗ Não foi possível comparar - dados inválidos")
        return
    
    # CPF/CNPJs únicos em cada base
    cpfs_boletos = set(boletos_validos['cpf_cnpj_limpo'].unique())
    cpfs_faturas = set(faturas_validas['cpf_cnpj_limpo'].unique())
    
    print(f"Clientes únicos em BOLETOS: {len(cpfs_boletos)}")
    print(f"Clientes únicos em FATURAS: {len(cpfs_faturas)}")
    
    # Interseção e diferenças
    intersecao = cpfs_boletos.intersection(cpfs_faturas)
    apenas_boletos = cpfs_boletos - cpfs_faturas
    apenas_faturas = cpfs_faturas - cpfs_boletos
    
    print(f"\nClientes presentes em AMBAS as bases: {len(intersecao)}")
    print(f"Clientes APENAS em boletos: {len(apenas_boletos)}")
    print(f"Clientes APENAS em faturas: {len(apenas_faturas)}")
    
    # Percentuais
    total_clientes_unicos = len(cpfs_boletos.union(cpfs_faturas))
    print(f"\nTotal de clientes únicos (união): {total_clientes_unicos}")
    print(f"Sobreposição: {len(intersecao)/total_clientes_unicos*100:.1f}%")
    
    return {
        'cpfs_boletos': cpfs_boletos,
        'cpfs_faturas': cpfs_faturas,
        'intersecao': intersecao,
        'apenas_boletos': apenas_boletos,
        'apenas_faturas': apenas_faturas
    }

def main():
    """Função principal"""
    print("ANÁLISE FINANCEIRA - BOLETOS vs FATURAS")
    print("="*60)
    
    # Carregar dados
    boletos, faturas = carregar_dados()
    
    if boletos is None or faturas is None:
        print("✗ Erro ao carregar dados. Encerrando análise.")
        return
    
    # Analisar estrutura
    boletos = analisar_estrutura(boletos, "BOLETOS")
    faturas = analisar_estrutura(faturas, "FATURAS")
    
    # Identificar campos CPF/CNPJ
    campos_boletos = identificar_campo_cpf_cnpj(boletos, "BOLETOS")
    campos_faturas = identificar_campo_cpf_cnpj(faturas, "FATURAS")
    
    # Para continuar, vou assumir os campos mais prováveis
    # Você pode ajustar conforme necessário
    campo_boletos = campos_boletos[0] if campos_boletos else None
    campo_faturas = campos_faturas[0] if campos_faturas else None
    
    if not campo_boletos or not campo_faturas:
        print("✗ Não foi possível identificar campos de CPF/CNPJ automaticamente")
        print("Por favor, verifique os dados e ajuste o script")
        return
    
    # Analisar clientes únicos
    boletos_validos = analisar_clientes_unicos(boletos, campo_boletos, "BOLETOS")
    faturas_validas = analisar_clientes_unicos(faturas, campo_faturas, "FATURAS")
    
    # Analisar métricas financeiras
    analisar_metricas_financeiras(boletos, "BOLETOS")
    analisar_metricas_financeiras(faturas, "FATURAS")
    
    # Comparar bases
    comparacao = comparar_bases(boletos_validos, faturas_validas)
    
    print(f"\n{'='*60}")
    print("ANÁLISE CONCLUÍDA!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
