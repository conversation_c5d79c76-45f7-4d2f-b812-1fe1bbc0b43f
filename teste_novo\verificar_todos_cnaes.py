import pandas as pd

def verificar_todos_cnaes():
    """Verifica se todos os CNAEs estão sendo capturados"""
    print("VERIFICANDO CAPTURA DE TODOS OS CNAEs")
    print("="*50)
    
    try:
        # Carregar arquivo com todos os CNAEs
        df_todos_cnaes = pd.read_excel("teste_novo/arquivo_final_boletos_todos_cnaes.xlsx")
        
        print("✓ Arquivo carregado com sucesso!")
        
        print(f"\n📊 ESTATÍSTICAS GERAIS:")
        print(f"- Total de registros: {len(df_todos_cnaes)}")
        print(f"- Clientes com CNAE: {df_todos_cnaes['CNAE'].notna().sum()}")
        print(f"- Clientes com múltiplos CNAEs: {df_todos_cnaes['CNAE'].str.contains(';', na=False).sum()}")
        
        # Analisar múltiplos CNAEs
        clientes_multiplos = df_todos_cnaes[df_todos_cnaes['CNAE'].str.contains(';', na=False)]
        
        print(f"\n🏢 CLIENTES COM MÚLTIPLOS CNAEs ({len(clientes_multiplos)} clientes):")
        print(f"{'Nome':<40} | {'Qtd CNAEs':<10} | {'CNAEs'}")
        print("-" * 100)
        
        for idx, row in clientes_multiplos.head(10).iterrows():
            nome = row['Nome'][:35] if row['Nome'] else 'N/A'
            cnaes = row['CNAE']
            qtd_cnaes = len(cnaes.split(';'))
            cnaes_resumo = cnaes[:50] + '...' if len(cnaes) > 50 else cnaes
            print(f"{nome:<40} | {qtd_cnaes:<10} | {cnaes_resumo}")
        
        # Estatísticas de quantidade de CNAEs
        print(f"\n📈 DISTRIBUIÇÃO DE QUANTIDADE DE CNAEs:")
        for qtd in range(1, 11):
            if qtd == 1:
                count = len(df_todos_cnaes[~df_todos_cnaes['CNAE'].str.contains(';', na=False) & df_todos_cnaes['CNAE'].notna()])
            else:
                count = len(df_todos_cnaes[df_todos_cnaes['CNAE'].str.count(';') == qtd-1])
            
            if count > 0:
                print(f"  {qtd} CNAE{'s' if qtd > 1 else ' '}: {count:3d} clientes")
        
        # Verificar descrições de CNAE
        print(f"\n📋 DESCRIÇÕES DE CNAE:")
        cnaes_com_descricao = df_todos_cnaes['Descricao_CNAE'].notna().sum()
        total_com_cnae = df_todos_cnaes['Primeiro_CNAE'].notna().sum()
        print(f"- CNAEs com descrição: {cnaes_com_descricao}/{total_com_cnae} ({cnaes_com_descricao/total_com_cnae*100:.1f}%)")
        
        # Mostrar exemplos de descrições
        print(f"\nExemplos de CNAEs com descrição:")
        exemplos_descricao = df_todos_cnaes[df_todos_cnaes['Descricao_CNAE'].notna()][['Nome', 'Primeiro_CNAE', 'Descricao_CNAE']].head(5)
        for idx, row in exemplos_descricao.iterrows():
            nome = row['Nome'][:25] if row['Nome'] else 'N/A'
            print(f"  {nome:<25} | {row['Primeiro_CNAE']} | {row['Descricao_CNAE'][:40]}...")
        
        # Verificar formato das datas
        print(f"\n📅 FORMATO DAS DATAS:")
        datas_exemplo = df_todos_cnaes[['Data_Primeiro_Boleto', 'Data_Ultimo_Boleto']].head(3)
        for idx, row in datas_exemplo.iterrows():
            print(f"  Cliente {idx+1}: {row['Data_Primeiro_Boleto']} até {row['Data_Ultimo_Boleto']}")
        
        # Comparar com arquivo anterior
        try:
            df_anterior = pd.read_excel("teste_novo/arquivo_final_boletos_melhorado.xlsx")
            
            print(f"\n🔄 COMPARAÇÃO COM ARQUIVO ANTERIOR:")
            print(f"{'Métrica':<30} | {'Anterior':<10} | {'Atual':<10} | {'Diferença'}")
            print("-" * 65)
            
            metricas = [
                ('Total registros', len(df_anterior), len(df_todos_cnaes)),
                ('Com CNAE', df_anterior['CNAE'].notna().sum(), df_todos_cnaes['CNAE'].notna().sum()),
                ('Com múltiplos CNAEs', df_anterior['CNAE'].str.contains(';', na=False).sum(), df_todos_cnaes['CNAE'].str.contains(';', na=False).sum()),
                ('Com descrição CNAE', df_anterior['Descricao_CNAE'].notna().sum(), df_todos_cnaes['Descricao_CNAE'].notna().sum())
            ]
            
            for nome, anterior, atual in metricas:
                diferenca = atual - anterior
                sinal = '+' if diferenca > 0 else ''
                print(f"{nome:<30} | {anterior:<10} | {atual:<10} | {sinal}{diferenca}")
                
        except:
            print("Arquivo anterior não encontrado para comparação")
        
        # Mostrar cliente com mais CNAEs
        if len(clientes_multiplos) > 0:
            cliente_max_cnaes = clientes_multiplos.loc[clientes_multiplos['CNAE'].str.count(';').idxmax()]
            max_cnaes = len(cliente_max_cnaes['CNAE'].split(';'))
            
            print(f"\n🏆 CLIENTE COM MAIS CNAEs:")
            print(f"Nome: {cliente_max_cnaes['Nome']}")
            print(f"Quantidade: {max_cnaes} CNAEs")
            print(f"CNAEs: {cliente_max_cnaes['CNAE']}")
        
        return df_todos_cnaes
        
    except Exception as e:
        print(f"✗ Erro: {e}")
        return None

def main():
    df = verificar_todos_cnaes()
    
    if df is not None:
        print(f"\n" + "="*50)
        print("✅ TODOS OS CNAEs CAPTURADOS COM SUCESSO!")
        print("="*50)
        print(f"📁 Localização: teste_novo/arquivo_final_boletos_todos_cnaes.xlsx")
        print(f"\n🎯 MELHORIAS IMPLEMENTADAS:")
        print(f"   ✅ Nomes corretos (BOLETOS, não faturas)")
        print(f"   ✅ Datas formatadas como DD/MM/AAAA")
        print(f"   ✅ TODOS os CNAEs capturados (separados por ;)")
        print(f"   ✅ Descrições de CNAE baseadas no primeiro CNAE")
        print(f"   ✅ {df['CNAE'].str.contains(';', na=False).sum()} clientes com múltiplos CNAEs")
        print(f"\n🚀 Arquivo final completo e correto!")

if __name__ == "__main__":
    main()
