import pandas as pd
import numpy as np

def verificar_arquivo_final():
    """Verifica o arquivo final gerado"""
    print("VERIFICAÇÃO DO ARQUIVO FINAL GERADO")
    print("="*50)
    
    try:
        # Carregar arquivo gerado
        df_gerado = pd.read_excel("teste_novo/arquivo_final_boletos.xlsx")
        
        # Carregar arquivo original para comparação
        df_original = pd.read_excel("data/arquivo_final.xlsx")
        
        print("✓ Arquivos carregados com sucesso!")
        
        # Comparar estruturas
        print(f"\nComparação de estruturas:")
        print(f"- Original: {df_original.shape[0]} linhas x {df_original.shape[1]} colunas")
        print(f"- Gerado:   {df_gerado.shape[0]} linhas x {df_gerado.shape[1]} colunas")
        
        # Verificar se as colunas são idênticas
        colunas_original = set(df_original.columns)
        colunas_gerado = set(df_gerado.columns)
        
        if colunas_original == colunas_gerado:
            print("✓ Estrutura de colunas idêntica!")
        else:
            print("⚠️  Diferenças nas colunas:")
            if colunas_original - colunas_gerado:
                print(f"  Faltando: {colunas_original - colunas_gerado}")
            if colunas_gerado - colunas_original:
                print(f"  Extras: {colunas_gerado - colunas_original}")
        
        # Mostrar amostra dos dados
        print(f"\nAmostra dos dados gerados:")
        print(df_gerado.head(3).to_string())
        
        # Estatísticas principais
        print(f"\nEstatísticas principais:")
        print(f"- Total de clientes: {len(df_gerado)}")
        print(f"- Clientes PF: {len(df_gerado[df_gerado['Tipo_Destinatario'] == 'PF'])}")
        print(f"- Clientes PJ: {len(df_gerado[df_gerado['Tipo_Destinatario'] == 'PJ'])}")
        print(f"- Valor total: R$ {df_gerado['Valor_Total_Faturado'].sum():,.2f}")
        print(f"- Valor médio por cliente: R$ {df_gerado['Valor_Total_Faturado'].mean():,.2f}")
        print(f"- Período médio: {df_gerado['Periodo_Cliente_Meses'].mean():.1f} meses")
        
        # Top 5 clientes
        print(f"\nTop 5 clientes por valor:")
        top5 = df_gerado.nlargest(5, 'Valor_Total_Faturado')[['Nome', 'Valor_Total_Faturado', 'Total_Faturas_Unicas']]
        for idx, row in top5.iterrows():
            print(f"  {row['Nome'][:40]:<40} R$ {row['Valor_Total_Faturado']:>12,.2f} ({row['Total_Faturas_Unicas']} boletos)")
        
        # Verificar qualidade dos dados
        print(f"\nQualidade dos dados:")
        
        # Valores negativos
        negativos = (df_gerado['Valor_Total_Faturado'] < 0).sum()
        if negativos > 0:
            print(f"⚠️  {negativos} clientes com valor total negativo (estornos > recebimentos)")
        else:
            print("✓ Todos os valores totais são positivos")
        
        # Dados faltantes importantes
        campos_importantes = ['Nome', 'CPF_CNPJ', 'Tipo_Destinatario', 'Valor_Total_Faturado']
        for campo in campos_importantes:
            faltantes = df_gerado[campo].isna().sum()
            if faltantes > 0:
                print(f"⚠️  {campo}: {faltantes} valores faltantes")
            else:
                print(f"✓ {campo}: sem valores faltantes")
        
        # Verificar consistência de datas
        datas_inconsistentes = (df_gerado['Data_Primeira_Fatura'] > df_gerado['Data_Ultima_Fatura']).sum()
        if datas_inconsistentes > 0:
            print(f"⚠️  {datas_inconsistentes} registros com data primeira > data última")
        else:
            print("✓ Datas consistentes")
        
        print(f"\n" + "="*50)
        print("RESUMO FINAL")
        print("="*50)
        print(f"📁 Arquivo gerado: teste_novo/arquivo_final_boletos.xlsx")
        print(f"📊 Formato: Idêntico ao arquivo_final.xlsx original")
        print(f"🔄 Dados: Substituídos de faturas para boletos")
        print(f"📈 Registros: {len(df_gerado)} clientes únicos")
        print(f"💰 Valor total: R$ {df_gerado['Valor_Total_Faturado'].sum():,.2f}")
        print(f"📅 Período: {df_gerado['Data_Primeira_Fatura'].min().strftime('%d/%m/%Y')} a {df_gerado['Data_Ultima_Fatura'].max().strftime('%d/%m/%Y')}")
        
        return df_gerado
        
    except Exception as e:
        print(f"✗ Erro: {e}")
        return None

def main():
    df = verificar_arquivo_final()
    
    if df is not None:
        print(f"\n✅ SUCESSO! Arquivo criado com a mesma estrutura do arquivo_final.xlsx")
        print(f"   mas usando dados dos boletos ao invés das faturas.")
        print(f"\n📋 Principais mudanças realizadas:")
        print(f"   • Todas as métricas de 'fatura' foram substituídas por métricas de 'boleto'")
        print(f"   • Dados de clientes vêm das bases PF e PJ")
        print(f"   • Cálculos incluem estornos/devoluções (valores negativos)")
        print(f"   • Período do cliente calculado com +1 mês conforme sua preferência")
        print(f"   • Valores arredondados para 2 casas decimais")

if __name__ == "__main__":
    main()
