import pandas as pd
import numpy as np
from datetime import datetime
import re

def limpar_cpf_cnpj(valor):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(valor):
        return None
    
    # Converter para string e remover caracteres especiais
    valor_str = str(valor).strip()
    valor_limpo = ''.join(filter(str.isdigit, valor_str))
    
    # Retornar apenas se tiver tamanho válido (11 para CPF, 14 para CNPJ)
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def analisar_boletos():
    """Analisa os dados de boletos"""
    print("ANÁLISE DOS BOLETOS")
    print("="*50)
    
    # Carregar dados
    boletos = pd.read_excel("data/boletos_legalone.xlsx")
    print(f"Total de registros: {len(boletos):,}")
    
    # Renomear colunas para facilitar
    boletos.columns = ['data_recebimento', 'valor_liquido', 'nome_cliente', 'cpf_cnpj']
    
    # Limpar CPF/CNPJ
    boletos['cpf_cnpj_limpo'] = boletos['cpf_cnpj'].apply(limpar_cpf_cnpj)
    
    # Filtrar apenas registros com CPF/CNPJ válido
    boletos_validos = boletos.dropna(subset=['cpf_cnpj_limpo'])
    print(f"Registros com CPF/CNPJ válido: {len(boletos_validos):,}")
    print(f"Registros sem CPF/CNPJ válido: {len(boletos) - len(boletos_validos):,}")
    
    # Clientes únicos
    clientes_unicos = boletos_validos['cpf_cnpj_limpo'].nunique()
    print(f"Clientes únicos: {clientes_unicos:,}")
    
    # Análise de valores
    valores = boletos_validos['valor_liquido'].dropna()
    print(f"\nMÉTRICAS DE VALORES:")
    print(f"Total recebido: R$ {valores.sum():,.2f}")
    print(f"Valor médio por boleto: R$ {valores.mean():,.2f}")
    print(f"Valor mediano: R$ {valores.median():,.2f}")
    print(f"Menor valor: R$ {valores.min():,.2f}")
    print(f"Maior valor: R$ {valores.max():,.2f}")
    
    # Análise por cliente
    por_cliente = boletos_validos.groupby('cpf_cnpj_limpo').agg({
        'valor_liquido': ['count', 'sum', 'mean'],
        'data_recebimento': ['min', 'max']
    }).round(2)
    
    por_cliente.columns = ['qtd_boletos', 'total_recebido', 'valor_medio', 'primeiro_pagamento', 'ultimo_pagamento']
    
    print(f"\nMÉTRICAS POR CLIENTE:")
    print(f"Média de boletos por cliente: {por_cliente['qtd_boletos'].mean():.1f}")
    print(f"Cliente com mais boletos: {por_cliente['qtd_boletos'].max()} boletos")
    print(f"Cliente que mais pagou: R$ {por_cliente['total_recebido'].max():,.2f}")
    
    # Top 10 clientes por valor
    top_clientes = por_cliente.sort_values('total_recebido', ascending=False).head(10)
    print(f"\nTOP 10 CLIENTES POR VALOR TOTAL:")
    for i, (cpf_cnpj, dados) in enumerate(top_clientes.iterrows(), 1):
        print(f"{i:2d}. {cpf_cnpj}: R$ {dados['total_recebido']:,.2f} ({dados['qtd_boletos']} boletos)")
    
    # Análise temporal
    boletos_validos['data_recebimento'] = pd.to_datetime(boletos_validos['data_recebimento'])
    boletos_validos['ano'] = boletos_validos['data_recebimento'].dt.year
    boletos_validos['mes'] = boletos_validos['data_recebimento'].dt.month
    
    por_ano = boletos_validos.groupby('ano').agg({
        'valor_liquido': 'sum',
        'cpf_cnpj_limpo': 'nunique'
    }).round(2)
    
    print(f"\nRECEBIMENTO POR ANO:")
    for ano, dados in por_ano.iterrows():
        print(f"{ano}: R$ {dados['valor_liquido']:,.2f} ({dados['cpf_cnpj_limpo']} clientes únicos)")
    
    return boletos_validos, por_cliente

def analisar_faturas():
    """Analisa os dados de faturas"""
    print(f"\n{'='*50}")
    print("ANÁLISE DAS FATURAS")
    print("="*50)
    
    # Carregar dados
    faturas = pd.read_excel("data/faturas_emitidas.xlsx")
    print(f"Total de registros: {len(faturas):,}")
    
    # Renomear colunas para facilitar
    faturas.columns = ['status', 'data_emissao', 'numero_fatura', 'agrupado_por', 'destinatario', 'tipo', 'valor']
    
    # Extrair CPF/CNPJ do campo destinatário (assumindo que está no formato "Nome - CPF/CNPJ")
    def extrair_cpf_cnpj_destinatario(destinatario):
        if pd.isna(destinatario):
            return None
        
        # Procurar por padrões de CPF (xxx.xxx.xxx-xx) ou CNPJ (xx.xxx.xxx/xxxx-xx)
        padrao_cpf = r'\d{3}\.\d{3}\.\d{3}-\d{2}'
        padrao_cnpj = r'\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2}'
        
        match_cnpj = re.search(padrao_cnpj, str(destinatario))
        if match_cnpj:
            return limpar_cpf_cnpj(match_cnpj.group())
        
        match_cpf = re.search(padrao_cpf, str(destinatario))
        if match_cpf:
            return limpar_cpf_cnpj(match_cpf.group())
        
        # Se não encontrar padrão, tentar extrair apenas números
        numeros = ''.join(filter(str.isdigit, str(destinatario)))
        if len(numeros) in [11, 14]:
            return numeros
        
        return None
    
    faturas['cpf_cnpj_limpo'] = faturas['destinatario'].apply(extrair_cpf_cnpj_destinatario)
    
    # Filtrar apenas registros com CPF/CNPJ válido
    faturas_validas = faturas.dropna(subset=['cpf_cnpj_limpo'])
    print(f"Registros com CPF/CNPJ válido: {len(faturas_validas):,}")
    print(f"Registros sem CPF/CNPJ válido: {len(faturas) - len(faturas_validas):,}")
    
    # Clientes únicos
    clientes_unicos = faturas_validas['cpf_cnpj_limpo'].nunique()
    print(f"Clientes únicos: {clientes_unicos:,}")
    
    # Análise de valores
    valores = faturas_validas['valor'].dropna()
    print(f"\nMÉTRICAS DE VALORES:")
    print(f"Total faturado: R$ {valores.sum():,.2f}")
    print(f"Valor médio por fatura: R$ {valores.mean():,.2f}")
    print(f"Valor mediano: R$ {valores.median():,.2f}")
    print(f"Menor valor: R$ {valores.min():,.2f}")
    print(f"Maior valor: R$ {valores.max():,.2f}")
    
    # Análise por status
    por_status = faturas_validas.groupby('status').agg({
        'valor': ['count', 'sum']
    }).round(2)
    
    print(f"\nFATURAS POR STATUS:")
    for status, dados in por_status.iterrows():
        qtd = dados[('valor', 'count')]
        total = dados[('valor', 'sum')]
        print(f"{status}: {qtd} faturas - R$ {total:,.2f}")
    
    # Análise por tipo
    por_tipo = faturas_validas.groupby('tipo').agg({
        'valor': ['count', 'sum']
    }).round(2)
    
    print(f"\nFATURAS POR TIPO:")
    for tipo, dados in por_tipo.iterrows():
        qtd = dados[('valor', 'count')]
        total = dados[('valor', 'sum')]
        print(f"{tipo}: {qtd} faturas - R$ {total:,.2f}")
    
    # Análise por cliente
    por_cliente = faturas_validas.groupby('cpf_cnpj_limpo').agg({
        'valor': ['count', 'sum', 'mean'],
        'data_emissao': ['min', 'max']
    }).round(2)
    
    por_cliente.columns = ['qtd_faturas', 'total_faturado', 'valor_medio', 'primeira_fatura', 'ultima_fatura']
    
    print(f"\nMÉTRICAS POR CLIENTE:")
    print(f"Média de faturas por cliente: {por_cliente['qtd_faturas'].mean():.1f}")
    print(f"Cliente com mais faturas: {por_cliente['qtd_faturas'].max()} faturas")
    print(f"Cliente com maior faturamento: R$ {por_cliente['total_faturado'].max():,.2f}")
    
    # Top 10 clientes por valor
    top_clientes = por_cliente.sort_values('total_faturado', ascending=False).head(10)
    print(f"\nTOP 10 CLIENTES POR VALOR TOTAL:")
    for i, (cpf_cnpj, dados) in enumerate(top_clientes.iterrows(), 1):
        print(f"{i:2d}. {cpf_cnpj}: R$ {dados['total_faturado']:,.2f} ({dados['qtd_faturas']} faturas)")
    
    return faturas_validas, por_cliente

def comparar_bases(boletos_validos, faturas_validas):
    """Compara as duas bases de dados"""
    print(f"\n{'='*60}")
    print("COMPARAÇÃO ENTRE BOLETOS E FATURAS")
    print("="*60)
    
    # CPF/CNPJs únicos em cada base
    cpfs_boletos = set(boletos_validos['cpf_cnpj_limpo'].unique())
    cpfs_faturas = set(faturas_validas['cpf_cnpj_limpo'].unique())
    
    print(f"Clientes únicos em BOLETOS: {len(cpfs_boletos):,}")
    print(f"Clientes únicos em FATURAS: {len(cpfs_faturas):,}")
    
    # Interseção e diferenças
    intersecao = cpfs_boletos.intersection(cpfs_faturas)
    apenas_boletos = cpfs_boletos - cpfs_faturas
    apenas_faturas = cpfs_faturas - cpfs_boletos
    
    print(f"\nClientes presentes em AMBAS as bases: {len(intersecao):,}")
    print(f"Clientes APENAS em boletos: {len(apenas_boletos):,}")
    print(f"Clientes APENAS em faturas: {len(apenas_faturas):,}")
    
    # Percentuais
    total_clientes_unicos = len(cpfs_boletos.union(cpfs_faturas))
    print(f"\nTotal de clientes únicos (união): {total_clientes_unicos:,}")
    
    if total_clientes_unicos > 0:
        print(f"Sobreposição: {len(intersecao)/total_clientes_unicos*100:.1f}%")
        print(f"Exclusivos de boletos: {len(apenas_boletos)/total_clientes_unicos*100:.1f}%")
        print(f"Exclusivos de faturas: {len(apenas_faturas)/total_clientes_unicos*100:.1f}%")
    
    # Análise de valores para clientes em comum
    if len(intersecao) > 0:
        print(f"\nANÁLISE DOS CLIENTES EM COMUM ({len(intersecao):,} clientes):")
        
        # Valores totais dos clientes em comum
        boletos_comum = boletos_validos[boletos_validos['cpf_cnpj_limpo'].isin(intersecao)]
        faturas_comum = faturas_validas[faturas_validas['cpf_cnpj_limpo'].isin(intersecao)]
        
        total_recebido_comum = boletos_comum['valor_liquido'].sum()
        total_faturado_comum = faturas_comum['valor'].sum()
        
        print(f"Total recebido (boletos): R$ {total_recebido_comum:,.2f}")
        print(f"Total faturado (faturas): R$ {total_faturado_comum:,.2f}")
        
        if total_faturado_comum > 0:
            taxa_recebimento = (total_recebido_comum / total_faturado_comum) * 100
            print(f"Taxa de recebimento: {taxa_recebimento:.1f}%")
    
    return {
        'cpfs_boletos': cpfs_boletos,
        'cpfs_faturas': cpfs_faturas,
        'intersecao': intersecao,
        'apenas_boletos': apenas_boletos,
        'apenas_faturas': apenas_faturas
    }

def main():
    """Função principal"""
    print("ANÁLISE FINANCEIRA DETALHADA - BOLETOS vs FATURAS")
    print("="*70)
    
    # Analisar boletos
    boletos_validos, boletos_por_cliente = analisar_boletos()
    
    # Analisar faturas
    faturas_validas, faturas_por_cliente = analisar_faturas()
    
    # Comparar bases
    comparacao = comparar_bases(boletos_validos, faturas_validas)
    
    print(f"\n{'='*70}")
    print("RESUMO EXECUTIVO")
    print("="*70)
    print(f"• Total de registros de boletos: {len(boletos_validos):,}")
    print(f"• Total de registros de faturas: {len(faturas_validas):,}")
    print(f"• Clientes únicos em boletos: {len(comparacao['cpfs_boletos']):,}")
    print(f"• Clientes únicos em faturas: {len(comparacao['cpfs_faturas']):,}")
    print(f"• Clientes em ambas as bases: {len(comparacao['intersecao']):,}")
    print(f"• Total de clientes únicos: {len(comparacao['cpfs_boletos'].union(comparacao['cpfs_faturas'])):,}")
    
    total_recebido = boletos_validos['valor_liquido'].sum()
    total_faturado = faturas_validas['valor'].sum()
    print(f"• Total recebido (boletos): R$ {total_recebido:,.2f}")
    print(f"• Total faturado (faturas): R$ {total_faturado:,.2f}")
    
    print(f"\n{'='*70}")
    print("ANÁLISE CONCLUÍDA!")
    print("="*70)

if __name__ == "__main__":
    main()
