import pandas as pd
import numpy as np

def examinar_arquivo(caminho, nome):
    """Examina a estrutura de um arquivo"""
    print(f"\nEXAMINANDO {nome.upper()}")
    print("="*50)
    
    try:
        # Carregar arquivo
        df = pd.read_excel(caminho)
        print(f"✓ Arquivo carregado com sucesso!")
        print(f"Dimensões: {df.shape[0]} linhas x {df.shape[1]} colunas")
        
        # Mostrar colunas
        print(f"\nColunas ({len(df.columns)}):")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # Mostrar primeiras linhas
        print(f"\nPrimeiras 3 linhas:")
        print(df.head(3))
        
        # Mostrar tipos de dados
        print(f"\nTipos de dados:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")
        
        return df
        
    except Exception as e:
        print(f"✗ Erro ao carregar arquivo: {e}")
        return None

def main():
    print("EXAMINANDO ARQUIVOS FONTE")
    print("="*60)
    
    # Examinar base PF
    df_pf = examinar_arquivo("data/base_pf_legalone.xlsx", "BASE PF")
    
    # Examinar base PJ
    df_pj = examinar_arquivo("data/base_pj_legalone.xlsx", "BASE PJ")
    
    # Examinar boletos
    df_boletos = examinar_arquivo("data/boletos_legalone.xlsx", "BOLETOS")
    
    # Salvar análise completa
    with open('teste_novo/analise_arquivos_fonte.txt', 'w', encoding='utf-8') as f:
        f.write("ANÁLISE DOS ARQUIVOS FONTE\n")
        f.write("="*60 + "\n\n")
        
        if df_pf is not None:
            f.write("BASE PF (PESSOA FÍSICA):\n")
            f.write(f"- Dimensões: {df_pf.shape[0]} linhas x {df_pf.shape[1]} colunas\n")
            f.write("- Colunas:\n")
            for i, col in enumerate(df_pf.columns, 1):
                f.write(f"  {i:2d}. {col}\n")
            f.write("\n")
        
        if df_pj is not None:
            f.write("BASE PJ (PESSOA JURÍDICA):\n")
            f.write(f"- Dimensões: {df_pj.shape[0]} linhas x {df_pj.shape[1]} colunas\n")
            f.write("- Colunas:\n")
            for i, col in enumerate(df_pj.columns, 1):
                f.write(f"  {i:2d}. {col}\n")
            f.write("\n")
        
        if df_boletos is not None:
            f.write("BOLETOS:\n")
            f.write(f"- Dimensões: {df_boletos.shape[0]} linhas x {df_boletos.shape[1]} colunas\n")
            f.write("- Colunas:\n")
            for i, col in enumerate(df_boletos.columns, 1):
                f.write(f"  {i:2d}. {col}\n")
            f.write("\n")
    
    print("\n✓ Análise salva em: teste_novo/analise_arquivos_fonte.txt")
    
    # Análise de mapeamento
    print("\n" + "="*60)
    print("ANÁLISE DE MAPEAMENTO PARA ARQUIVO_FINAL")
    print("="*60)
    
    print("\nColunas do arquivo_final.xlsx que precisam ser mapeadas:")
    colunas_final = [
        'Nome', 'CPF_CNPJ', 'Tipo_Destinatario', 
        'Meses_Desde_Ultima_Fatura', 'Data_Ultima_Fatura', 'Data_Primeira_Fatura',
        'Periodo_Cliente_Meses', 'Total_Faturas_Unicas', 'Valor_Total_Faturado',
        'Valor_Medio_Por_Fatura', 'Valor_Minimo', 'Valor_Maximo',
        'Frequencia_Faturas_Por_Mes', 'Telefones', 'Emails', 'UF',
        'CNAE', 'Regime_Tributario', 'Primeiro_CNAE', 'Descricao_CNAE'
    ]
    
    for col in colunas_final:
        print(f"- {col}")
    
    print("\nMapeamento necessário:")
    print("- Substituir todas as métricas de 'fatura' por métricas de 'boleto'")
    print("- Usar dados dos boletos para calcular valores, datas e frequências")
    print("- Usar bases PF/PJ para informações de cliente (telefones, emails, UF, CNAE, etc.)")

if __name__ == "__main__":
    main()
