import pandas as pd
import numpy as np

def comparar_estruturas():
    """Compara a estrutura do arquivo original com o gerado"""
    print("COMPARANDO ESTRUTURAS DOS ARQUIVOS")
    print("="*50)
    
    # Carregar arquivos
    try:
        df_original = pd.read_excel("data/arquivo_final.xlsx")
        df_gerado = pd.read_excel("teste_novo/arquivo_final_boletos.xlsx")
        
        print("✓ Ambos os arquivos carregados com sucesso!")
        
        # Comparar dimensões
        print(f"\nDimensões:")
        print(f"- Original: {df_original.shape[0]} linhas x {df_original.shape[1]} colunas")
        print(f"- Gerado:   {df_gerado.shape[0]} linhas x {df_gerado.shape[1]} colunas")
        
        # Comparar colunas
        print(f"\nColunas:")
        colunas_original = set(df_original.columns)
        colunas_gerado = set(df_gerado.columns)
        
        if colunas_original == colunas_gerado:
            print("✓ Colunas idênticas!")
        else:
            print("✗ Diferenças nas colunas:")
            if colunas_original - colunas_gerado:
                print(f"  Faltando no gerado: {colunas_original - colunas_gerado}")
            if colunas_gerado - colunas_original:
                print(f"  Extras no gerado: {colunas_gerado - colunas_original}")
        
        # Mostrar primeiras linhas do arquivo gerado
        print(f"\nPrimeiras 5 linhas do arquivo gerado:")
        print(df_gerado.head())
        
        # Comparar tipos de dados
        print(f"\nTipos de dados (gerado):")
        for col, dtype in df_gerado.dtypes.items():
            print(f"  {col}: {dtype}")
        
        # Estatísticas básicas
        print(f"\nEstatísticas do arquivo gerado:")
        print(f"- Clientes únicos: {df_gerado['CPF_CNPJ'].nunique()}")
        print(f"- Valor total: R$ {df_gerado['Valor_Total_Faturado'].sum():,.2f}")
        print(f"- Valor médio por cliente: R$ {df_gerado['Valor_Total_Faturado'].mean():,.2f}")
        print(f"- Período médio de relacionamento: {df_gerado['Periodo_Cliente_Meses'].mean():.1f} meses")
        
        # Verificar dados faltantes
        print(f"\nDados faltantes por coluna:")
        for col in df_gerado.columns:
            missing = df_gerado[col].isna().sum()
            if missing > 0:
                print(f"  {col}: {missing} ({missing/len(df_gerado)*100:.1f}%)")
        
        return df_original, df_gerado
        
    except Exception as e:
        print(f"✗ Erro ao carregar arquivos: {e}")
        return None, None

def verificar_qualidade_dados(df_gerado):
    """Verifica a qualidade dos dados gerados"""
    print(f"\n" + "="*50)
    print("VERIFICAÇÃO DE QUALIDADE DOS DADOS")
    print("="*50)
    
    # Verificar valores negativos
    colunas_valor = ['Valor_Total_Faturado', 'Valor_Medio_Por_Fatura', 'Valor_Minimo', 'Valor_Maximo']
    for col in colunas_valor:
        negativos = (df_gerado[col] < 0).sum()
        if negativos > 0:
            print(f"⚠️  {col}: {negativos} valores negativos")
        else:
            print(f"✓ {col}: sem valores negativos")
    
    # Verificar consistência de datas
    datas_inconsistentes = (df_gerado['Data_Primeira_Fatura'] > df_gerado['Data_Ultima_Fatura']).sum()
    if datas_inconsistentes > 0:
        print(f"⚠️  {datas_inconsistentes} registros com data primeira > data última")
    else:
        print("✓ Datas consistentes")
    
    # Verificar valores mínimo/máximo
    inconsistentes_min_max = (df_gerado['Valor_Minimo'] > df_gerado['Valor_Maximo']).sum()
    if inconsistentes_min_max > 0:
        print(f"⚠️  {inconsistentes_min_max} registros com valor mínimo > valor máximo")
    else:
        print("✓ Valores mínimo/máximo consistentes")
    
    # Verificar CPF/CNPJ únicos
    duplicados_cpf = df_gerado['CPF_CNPJ'].duplicated().sum()
    if duplicados_cpf > 0:
        print(f"⚠️  {duplicados_cpf} CPF/CNPJ duplicados")
    else:
        print("✓ CPF/CNPJ únicos")
    
    # Top 10 clientes por valor
    print(f"\nTop 10 clientes por valor recebido:")
    top_clientes = df_gerado.nlargest(10, 'Valor_Total_Faturado')[['Nome', 'Valor_Total_Faturado', 'Total_Faturas_Unicas']]
    for idx, row in top_clientes.iterrows():
        print(f"  {row['Nome'][:40]:<40} R$ {row['Valor_Total_Faturado']:>12,.2f} ({row['Total_Faturas_Unicas']} boletos)")

def main():
    df_original, df_gerado = comparar_estruturas()
    
    if df_gerado is not None:
        verificar_qualidade_dados(df_gerado)
        
        print(f"\n" + "="*50)
        print("ARQUIVO GERADO COM SUCESSO!")
        print("="*50)
        print(f"📁 Localização: teste_novo/arquivo_final_boletos.xlsx")
        print(f"📊 Registros: {len(df_gerado)}")
        print(f"💰 Valor total: R$ {df_gerado['Valor_Total_Faturado'].sum():,.2f}")
        print(f"📅 Período: {df_gerado['Data_Primeira_Fatura'].min().strftime('%d/%m/%Y')} a {df_gerado['Data_Ultima_Fatura'].max().strftime('%d/%m/%Y')}")

if __name__ == "__main__":
    main()
