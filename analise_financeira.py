import pandas as pd
import numpy as np

def analisar_arquivos_financeiros():
    """
    Analisa métricas relacionais entre boletos e faturas
    """
    print("📊 ANÁLISE FINANCEIRA - BOLETOS vs FATURAS")
    print("=" * 60)
    
    # Carregar arquivos
    try:
        print("📁 Carregando arquivos...")
        df_boletos = pd.read_excel('data/boletos_legalone.xlsx')
        df_faturas = pd.read_excel('data/faturas_emitidas.xlsx')
        print("✅ Arquivos carregados com sucesso!")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivos: {e}")
        return
    
    print(f"\n📋 ESTRUTURA DOS DADOS:")
    print(f"Boletos: {len(df_boletos)} registros, {len(df_boletos.columns)} colunas")
    print(f"Faturas: {len(df_faturas)} registros, {len(df_faturas.columns)} colunas")
    
    # Mostrar colunas
    print(f"\n🔍 COLUNAS - BOLETOS:")
    print(list(df_boletos.columns))
    print(f"\n🔍 COLUNAS - FATURAS:")
    print(list(df_faturas.columns))
    
    # Identificar coluna de CPF/CNPJ em cada arquivo
    colunas_boletos = [col.lower() for col in df_boletos.columns]
    colunas_faturas = [col.lower() for col in df_faturas.columns]
    
    # Buscar colunas de identificação
    col_id_boletos = None
    col_id_faturas = None
    
    for col in df_boletos.columns:
        if any(term in col.lower() for term in ['cpf', 'cnpj', 'documento']):
            col_id_boletos = col
            break
    
    for col in df_faturas.columns:
        if any(term in col.lower() for term in ['cpf', 'cnpj', 'documento']):
            col_id_faturas = col
            break
    
    print(f"\n🆔 COLUNAS DE IDENTIFICAÇÃO:")
    print(f"Boletos: {col_id_boletos}")
    print(f"Faturas: {col_id_faturas}")
    
    if col_id_boletos and col_id_faturas:
        # Análise de clientes únicos
        clientes_boletos = df_boletos[col_id_boletos].dropna().unique()
        clientes_faturas = df_faturas[col_id_faturas].dropna().unique()
        
        print(f"\n👥 CLIENTES ÚNICOS:")
        print(f"Boletos: {len(clientes_boletos)} clientes únicos")
        print(f"Faturas: {len(clientes_faturas)} clientes únicos")
        
        # Interseção
        clientes_comuns = set(clientes_boletos) & set(clientes_faturas)
        print(f"Comuns: {len(clientes_comuns)} clientes aparecem em ambos")
        
        # Exclusivos
        apenas_boletos = set(clientes_boletos) - set(clientes_faturas)
        apenas_faturas = set(clientes_faturas) - set(clientes_boletos)
        
        print(f"Apenas Boletos: {len(apenas_boletos)} clientes")
        print(f"Apenas Faturas: {len(apenas_faturas)} clientes")
        
        # Percentuais
        total_clientes_unicos = len(set(clientes_boletos) | set(clientes_faturas))
        print(f"\n📊 PERCENTUAIS:")
        print(f"Total de clientes únicos: {total_clientes_unicos}")
        print(f"Sobreposição: {len(clientes_comuns)/total_clientes_unicos*100:.1f}%")
        print(f"Exclusivos Boletos: {len(apenas_boletos)/total_clientes_unicos*100:.1f}%")
        print(f"Exclusivos Faturas: {len(apenas_faturas)/total_clientes_unicos*100:.1f}%")
    
    # Análise de valores (se existirem colunas de valor)
    print(f"\n💰 ANÁLISE DE VALORES:")
    
    # Buscar colunas de valor
    col_valor_boletos = None
    col_valor_faturas = None
    
    for col in df_boletos.columns:
        if any(term in col.lower() for term in ['valor', 'total', 'preco']):
            col_valor_boletos = col
            break
    
    for col in df_faturas.columns:
        if any(term in col.lower() for term in ['valor', 'total', 'preco']):
            col_valor_faturas = col
            break
    
    if col_valor_boletos:
        valor_total_boletos = df_boletos[col_valor_boletos].sum()
        print(f"Valor Total Boletos: R$ {valor_total_boletos:,.2f}")
        print(f"Valor Médio Boletos: R$ {df_boletos[col_valor_boletos].mean():,.2f}")
    
    if col_valor_faturas:
        valor_total_faturas = df_faturas[col_valor_faturas].sum()
        print(f"Valor Total Faturas: R$ {valor_total_faturas:,.2f}")
        print(f"Valor Médio Faturas: R$ {df_faturas[col_valor_faturas].mean():,.2f}")
    
    # Análise temporal (se existirem colunas de data)
    print(f"\n📅 ANÁLISE TEMPORAL:")
    
    for col in df_boletos.columns:
        if any(term in col.lower() for term in ['data', 'date']):
            try:
                df_boletos[col] = pd.to_datetime(df_boletos[col])
                print(f"Boletos - {col}: {df_boletos[col].min()} a {df_boletos[col].max()}")
            except:
                pass
    
    for col in df_faturas.columns:
        if any(term in col.lower() for term in ['data', 'date']):
            try:
                df_faturas[col] = pd.to_datetime(df_faturas[col])
                print(f"Faturas - {col}: {df_faturas[col].min()} a {df_faturas[col].max()}")
            except:
                pass
    
    print(f"\n" + "=" * 60)
    print("✅ Análise concluída!")

if __name__ == "__main__":
    analisar_arquivos_financeiros()