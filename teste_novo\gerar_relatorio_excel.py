import pandas as pd
import numpy as np
import re
from datetime import datetime

def limpar_cpf_cnpj(valor):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(valor):
        return None
    
    valor_str = str(valor).strip()
    valor_limpo = ''.join(filter(str.isdigit, valor_str))
    
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def limpar_nome(nome):
    """Limpa e padroniza nomes para comparação"""
    if pd.isna(nome):
        return ""
    
    nome_str = str(nome).upper().strip()
    nome_str = re.sub(r'[^\w\s]', ' ', nome_str)
    
    palavras_remover = ['LTDA', 'LTD', 'S/A', 'SA', 'EIRELI', 'EPP', 'ME', 'SOCIEDADE', 'CIA', 'COMPANHIA']
    for palavra in palavras_remover:
        nome_str = re.sub(rf'\b{palavra}\b', '', nome_str)
    
    nome_str = re.sub(r'\s+', ' ', nome_str).strip()
    return nome_str

def gerar_relatorio_excel():
    """Gera relatório completo em Excel"""
    print("GERANDO RELATÓRIO EXCEL COMPLETO")
    print("="*50)
    
    # Carregar e processar dados
    print("Carregando dados...")
    boletos = pd.read_excel("data/boletos_legalone.xlsx")
    boletos.columns = ['data_recebimento', 'valor_liquido', 'nome_cliente', 'cpf_cnpj']
    
    faturas = pd.read_excel("data/faturas_emitidas.xlsx")
    faturas.columns = ['status', 'data_emissao', 'numero_fatura', 'agrupado_por', 'destinatario', 'tipo', 'valor']
    
    # Processar boletos
    boletos['cpf_cnpj_limpo'] = boletos['cpf_cnpj'].apply(limpar_cpf_cnpj)
    boletos_validos = boletos.dropna(subset=['cpf_cnpj_limpo']).copy()
    boletos_validos['data_recebimento'] = pd.to_datetime(boletos_validos['data_recebimento'])
    
    # Criar mapeamento nome -> CPF/CNPJ
    mapeamento_nome_cpf = {}
    for _, row in boletos_validos.iterrows():
        nome = row['nome_cliente']
        cpf_cnpj = row['cpf_cnpj_limpo']
        
        if pd.notna(nome) and cpf_cnpj:
            nome_limpo = limpar_nome(nome)
            if nome_limpo.strip() != "":
                if nome_limpo not in mapeamento_nome_cpf:
                    mapeamento_nome_cpf[nome_limpo] = cpf_cnpj
    
    # Processar faturas
    faturas['cpf_cnpj_mapeado'] = None
    for idx, row in faturas.iterrows():
        nome_fatura = row['destinatario']
        nome_limpo = limpar_nome(nome_fatura)
        
        if nome_limpo in mapeamento_nome_cpf:
            faturas.at[idx, 'cpf_cnpj_mapeado'] = mapeamento_nome_cpf[nome_limpo]
    
    faturas_validas = faturas.dropna(subset=['cpf_cnpj_mapeado']).copy()
    faturas_validas['data_emissao'] = pd.to_datetime(faturas_validas['data_emissao'], format='%d/%m/%Y')
    
    # Criar arquivo Excel
    print("Criando arquivo Excel...")
    with pd.ExcelWriter('teste_novo/relatorio_financeiro_completo.xlsx', engine='openpyxl') as writer:
        
        # 1. Resumo Executivo
        resumo_data = {
            'Métrica': [
                'Total de registros de boletos',
                'Total de registros de faturas',
                'Clientes únicos em boletos',
                'Clientes únicos em faturas',
                'Total de clientes únicos',
                'Clientes em ambas as bases',
                'Clientes apenas em boletos',
                'Clientes apenas em faturas',
                'Total recebido (boletos)',
                'Total faturado (faturas)',
                'Taxa de recebimento (clientes comuns)'
            ],
            'Valor': [
                len(boletos_validos),
                len(faturas_validas),
                boletos_validos['cpf_cnpj_limpo'].nunique(),
                faturas_validas['cpf_cnpj_mapeado'].nunique(),
                len(set(boletos_validos['cpf_cnpj_limpo'].unique()).union(set(faturas_validas['cpf_cnpj_mapeado'].unique()))),
                len(set(boletos_validos['cpf_cnpj_limpo'].unique()).intersection(set(faturas_validas['cpf_cnpj_mapeado'].unique()))),
                len(set(boletos_validos['cpf_cnpj_limpo'].unique()) - set(faturas_validas['cpf_cnpj_mapeado'].unique())),
                len(set(faturas_validas['cpf_cnpj_mapeado'].unique()) - set(boletos_validos['cpf_cnpj_limpo'].unique())),
                f"R$ {boletos_validos['valor_liquido'].sum():,.2f}",
                f"R$ {faturas_validas['valor'].sum():,.2f}",
                f"{(boletos_validos[boletos_validos['cpf_cnpj_limpo'].isin(faturas_validas['cpf_cnpj_mapeado'])]['valor_liquido'].sum() / faturas_validas['valor'].sum() * 100):.1f}%"
            ]
        }
        
        resumo_df = pd.DataFrame(resumo_data)
        resumo_df.to_excel(writer, sheet_name='Resumo Executivo', index=False)
        
        # 2. Clientes Únicos por Base
        clientes_boletos = boletos_validos.groupby('cpf_cnpj_limpo').agg({
            'nome_cliente': 'first',
            'valor_liquido': ['count', 'sum', 'mean'],
            'data_recebimento': ['min', 'max']
        }).round(2)
        
        clientes_boletos.columns = ['nome', 'qtd_boletos', 'total_recebido', 'valor_medio', 'primeiro_pagamento', 'ultimo_pagamento']
        clientes_boletos = clientes_boletos.reset_index()
        clientes_boletos = clientes_boletos.sort_values('total_recebido', ascending=False)
        
        clientes_boletos.to_excel(writer, sheet_name='Clientes Boletos', index=False)
        
        clientes_faturas = faturas_validas.groupby('cpf_cnpj_mapeado').agg({
            'destinatario': 'first',
            'valor': ['count', 'sum', 'mean'],
            'data_emissao': ['min', 'max']
        }).round(2)
        
        clientes_faturas.columns = ['nome', 'qtd_faturas', 'total_faturado', 'valor_medio', 'primeira_fatura', 'ultima_fatura']
        clientes_faturas = clientes_faturas.reset_index()
        clientes_faturas = clientes_faturas.sort_values('total_faturado', ascending=False)
        
        clientes_faturas.to_excel(writer, sheet_name='Clientes Faturas', index=False)
        
        # 3. Clientes em Comum
        cpfs_comum = set(boletos_validos['cpf_cnpj_limpo'].unique()).intersection(set(faturas_validas['cpf_cnpj_mapeado'].unique()))
        
        if len(cpfs_comum) > 0:
            boletos_comum = boletos_validos[boletos_validos['cpf_cnpj_limpo'].isin(cpfs_comum)]
            faturas_comum = faturas_validas[faturas_validas['cpf_cnpj_mapeado'].isin(cpfs_comum)]
            
            # Consolidar dados dos clientes em comum
            comum_boletos = boletos_comum.groupby('cpf_cnpj_limpo').agg({
                'nome_cliente': 'first',
                'valor_liquido': ['count', 'sum'],
                'data_recebimento': ['min', 'max']
            }).round(2)
            
            comum_faturas = faturas_comum.groupby('cpf_cnpj_mapeado').agg({
                'valor': ['count', 'sum'],
                'data_emissao': ['min', 'max']
            }).round(2)
            
            # Merge dos dados
            clientes_comum_df = pd.merge(
                comum_boletos.reset_index(),
                comum_faturas.reset_index(),
                left_on='cpf_cnpj_limpo',
                right_on='cpf_cnpj_mapeado',
                how='inner'
            )
            
            # Renomear colunas
            clientes_comum_df.columns = [
                'cpf_cnpj', 'nome', 'qtd_boletos', 'total_recebido', 'primeiro_pagamento', 'ultimo_pagamento',
                'cpf_cnpj_dup', 'qtd_faturas', 'total_faturado', 'primeira_fatura', 'ultima_fatura'
            ]
            
            # Remover coluna duplicada
            clientes_comum_df = clientes_comum_df.drop('cpf_cnpj_dup', axis=1)
            
            # Calcular taxa de recebimento por cliente
            clientes_comum_df['taxa_recebimento'] = (clientes_comum_df['total_recebido'] / clientes_comum_df['total_faturado'] * 100).round(1)
            
            clientes_comum_df = clientes_comum_df.sort_values('total_recebido', ascending=False)
            clientes_comum_df.to_excel(writer, sheet_name='Clientes em Comum', index=False)
        
        # 4. Análise Temporal - Boletos
        boletos_por_ano = boletos_validos.groupby(boletos_validos['data_recebimento'].dt.year).agg({
            'valor_liquido': 'sum',
            'cpf_cnpj_limpo': 'nunique'
        }).round(2)
        
        boletos_por_ano.columns = ['total_recebido', 'clientes_unicos']
        boletos_por_ano = boletos_por_ano.reset_index()
        boletos_por_ano['ano'] = boletos_por_ano['data_recebimento'].astype(int)
        boletos_por_ano = boletos_por_ano[['ano', 'total_recebido', 'clientes_unicos']]
        
        boletos_por_ano.to_excel(writer, sheet_name='Boletos por Ano', index=False)
        
        # 5. Análise Temporal - Faturas
        faturas_por_ano = faturas_validas.groupby(faturas_validas['data_emissao'].dt.year).agg({
            'valor': 'sum',
            'cpf_cnpj_mapeado': 'nunique'
        }).round(2)
        
        faturas_por_ano.columns = ['total_faturado', 'clientes_unicos']
        faturas_por_ano = faturas_por_ano.reset_index()
        faturas_por_ano['ano'] = faturas_por_ano['data_emissao'].astype(int)
        faturas_por_ano = faturas_por_ano[['ano', 'total_faturado', 'clientes_unicos']]
        
        faturas_por_ano.to_excel(writer, sheet_name='Faturas por Ano', index=False)
        
        # 6. Top Clientes por Valor
        top_boletos = clientes_boletos.head(20)[['cpf_cnpj_limpo', 'nome', 'total_recebido', 'qtd_boletos']]
        top_boletos.to_excel(writer, sheet_name='Top 20 Boletos', index=False)
        
        top_faturas = clientes_faturas.head(20)[['cpf_cnpj_mapeado', 'nome', 'total_faturado', 'qtd_faturas']]
        top_faturas.to_excel(writer, sheet_name='Top 20 Faturas', index=False)
        
        # 7. Dados Brutos Processados
        boletos_export = boletos_validos[['cpf_cnpj_limpo', 'nome_cliente', 'data_recebimento', 'valor_liquido']].copy()
        boletos_export['data_recebimento'] = boletos_export['data_recebimento'].dt.strftime('%d/%m/%Y')
        boletos_export.to_excel(writer, sheet_name='Dados Boletos', index=False)
        
        faturas_export = faturas_validas[['cpf_cnpj_mapeado', 'destinatario', 'data_emissao', 'valor', 'status']].copy()
        faturas_export['data_emissao'] = faturas_export['data_emissao'].dt.strftime('%d/%m/%Y')
        faturas_export.to_excel(writer, sheet_name='Dados Faturas', index=False)
    
    print("✓ Relatório Excel gerado: teste_novo/relatorio_financeiro_completo.xlsx")
    
    # Gerar também um resumo em texto
    with open('teste_novo/resumo_metricas.txt', 'w', encoding='utf-8') as f:
        f.write("RESUMO DAS MÉTRICAS FINANCEIRAS\n")
        f.write("="*50 + "\n\n")
        
        f.write("📊 DADOS GERAIS:\n")
        f.write(f"• Total de registros de boletos: {len(boletos_validos):,}\n")
        f.write(f"• Total de registros de faturas: {len(faturas_validas):,}\n")
        f.write(f"• Clientes únicos em boletos: {boletos_validos['cpf_cnpj_limpo'].nunique():,}\n")
        f.write(f"• Clientes únicos em faturas: {faturas_validas['cpf_cnpj_mapeado'].nunique():,}\n")
        
        total_unicos = len(set(boletos_validos['cpf_cnpj_limpo'].unique()).union(set(faturas_validas['cpf_cnpj_mapeado'].unique())))
        intersecao = len(set(boletos_validos['cpf_cnpj_limpo'].unique()).intersection(set(faturas_validas['cpf_cnpj_mapeado'].unique())))
        
        f.write(f"• Total de clientes únicos: {total_unicos:,}\n\n")
        
        f.write("💰 VALORES:\n")
        f.write(f"• Total recebido (boletos): R$ {boletos_validos['valor_liquido'].sum():,.2f}\n")
        f.write(f"• Total faturado (faturas): R$ {faturas_validas['valor'].sum():,.2f}\n\n")
        
        f.write("🔗 RELACIONAMENTO:\n")
        f.write(f"• Clientes em ambas as bases: {intersecao:,} ({intersecao/total_unicos*100:.1f}%)\n")
        f.write(f"• Clientes apenas em boletos: {total_unicos - intersecao:,} ({(total_unicos - intersecao)/total_unicos*100:.1f}%)\n")
        f.write(f"• Clientes apenas em faturas: 0 (0.0%)\n")
        
        if intersecao > 0:
            boletos_comum = boletos_validos[boletos_validos['cpf_cnpj_limpo'].isin(faturas_validas['cpf_cnpj_mapeado'])]
            taxa_recebimento = (boletos_comum['valor_liquido'].sum() / faturas_validas['valor'].sum() * 100)
            f.write(f"• Taxa de recebimento (clientes comuns): {taxa_recebimento:.1f}%\n")
    
    print("✓ Resumo em texto gerado: teste_novo/resumo_metricas.txt")

def main():
    gerar_relatorio_excel()

if __name__ == "__main__":
    main()
