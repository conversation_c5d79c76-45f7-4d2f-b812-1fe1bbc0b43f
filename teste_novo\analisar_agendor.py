import pandas as pd
import numpy as np
import re

def limpar_cpf_cnpj(valor):
    """Limpa CPF/CNPJ removendo caracteres especiais"""
    if pd.isna(valor):
        return None
    
    valor_str = str(valor).strip()
    if valor_str in ['nan', 'None', '']:
        return None
    
    # Remove caracteres especiais
    valor_limpo = re.sub(r'[^\d]', '', valor_str)
    
    # Verifica se tem tamanho válido
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def analisar_agendor():
    """Analisa a estrutura do database_agendor.xlsx"""
    print("ANALISANDO DATABASE AGENDOR")
    print("="*50)
    
    try:
        df_agendor = pd.read_excel("data/database_agendor.xlsx")
        
        print(f"✓ Arquivo carregado: {len(df_agendor)} registros")
        
        # Analisar CPF/CNPJ
        print(f"\n📊 ANÁLISE CPF/CNPJ:")
        cpf_count = df_agendor['CPF'].notna().sum()
        cnpj_count = df_agendor['CNPJ'].notna().sum()
        total_docs = (df_agendor['CPF'].notna() | df_agendor['CNPJ'].notna()).sum()
        
        print(f"- Registros com CPF: {cpf_count}")
        print(f"- Registros com CNPJ: {cnpj_count}")
        print(f"- Total com CPF ou CNPJ: {total_docs}")
        
        # Exemplos de CPF
        if cpf_count > 0:
            print(f"\nExemplos de CPF:")
            cpfs_exemplo = df_agendor[df_agendor['CPF'].notna()]['CPF'].head(5)
            for i, cpf in enumerate(cpfs_exemplo):
                cpf_limpo = limpar_cpf_cnpj(cpf)
                print(f"  {i+1}. Original: {cpf} | Limpo: {cpf_limpo}")
        
        # Exemplos de CNPJ
        if cnpj_count > 0:
            print(f"\nExemplos de CNPJ:")
            cnpjs_exemplo = df_agendor[df_agendor['CNPJ'].notna()]['CNPJ'].head(5)
            for i, cnpj in enumerate(cnpjs_exemplo):
                cnpj_limpo = limpar_cpf_cnpj(cnpj)
                print(f"  {i+1}. Original: {cnpj} | Limpo: {cnpj_limpo}")
        
        # Campos úteis para complementar dados
        print(f"\n📋 CAMPOS ÚTEIS PARA COMPLEMENTAR DADOS:")
        campos_uteis = [
            'Empresa relacionada', 'Pessoa relacionada', 'E-mail', 'Telefone', 
            'Celular', 'WhatsApp', 'Estado', 'Cidade', 'Website'
        ]
        
        for campo in campos_uteis:
            if campo in df_agendor.columns:
                count = df_agendor[campo].notna().sum()
                pct = count / len(df_agendor) * 100
                print(f"  {campo:<25}: {count:4d} registros ({pct:5.1f}%)")
        
        # Criar dataset limpo para matching
        print(f"\n🔧 PREPARANDO DADOS PARA MATCHING:")
        
        # Limpar CPF/CNPJ
        df_agendor['cpf_limpo'] = df_agendor['CPF'].apply(limpar_cpf_cnpj)
        df_agendor['cnpj_limpo'] = df_agendor['CNPJ'].apply(limpar_cpf_cnpj)
        
        # Criar coluna unificada
        df_agendor['cpf_cnpj_limpo'] = df_agendor['cpf_limpo'].fillna(df_agendor['cnpj_limpo'])
        
        # Contar registros válidos
        registros_validos = df_agendor['cpf_cnpj_limpo'].notna().sum()
        print(f"  ✓ {registros_validos} registros com CPF/CNPJ válido para matching")
        
        # Verificar duplicatas
        duplicatas = df_agendor[df_agendor['cpf_cnpj_limpo'].duplicated(keep=False)]['cpf_cnpj_limpo'].nunique()
        print(f"  ⚠️ {duplicatas} CPF/CNPJs duplicados no Agendor")
        
        # Mostrar exemplo de registro completo
        print(f"\n📄 EXEMPLO DE REGISTRO COMPLETO:")
        registro_exemplo = df_agendor[df_agendor['cpf_cnpj_limpo'].notna()].iloc[0]
        
        campos_exemplo = [
            'Empresa relacionada', 'Pessoa relacionada', 'CPF', 'CNPJ', 
            'E-mail', 'Telefone', 'Celular', 'Estado', 'Cidade'
        ]
        
        for campo in campos_exemplo:
            if campo in df_agendor.columns:
                valor = registro_exemplo[campo]
                print(f"  {campo:<20}: {valor}")
        
        return df_agendor
        
    except Exception as e:
        print(f"✗ Erro ao carregar Agendor: {e}")
        return None

def main():
    df_agendor = analisar_agendor()
    
    if df_agendor is not None:
        print(f"\n" + "="*50)
        print("✅ AGENDOR ANALISADO COM SUCESSO!")
        print("="*50)
        print(f"🎯 Pronto para implementar busca complementar")

if __name__ == "__main__":
    main()
