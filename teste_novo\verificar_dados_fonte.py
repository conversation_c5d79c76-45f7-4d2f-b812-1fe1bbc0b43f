import pandas as pd
import numpy as np

def examinar_base_pf():
    """Examina a estrutura da base PF"""
    print("EXAMINANDO BASE PF")
    print("="*50)
    
    df_pf = pd.read_excel("data/base_pf_legalone.xlsx")
    
    print(f"Total de registros: {len(df_pf)}")
    print(f"Colunas disponíveis:")
    for i, col in enumerate(df_pf.columns):
        print(f"  {i+1:2d}. {col}")
    
    print(f"\nPrimeiras 3 linhas:")
    print(df_pf.head(3).to_string())
    
    # Verificar dados de contato
    print(f"\nAnálise de dados de contato:")
    
    # Telefones
    colunas_telefone = [col for col in df_pf.columns if 'telefone' in col.lower() or 'fone' in col.lower()]
    print(f"Colunas de telefone encontradas: {colunas_telefone}")
    
    for col in colunas_telefone:
        nao_nulos = df_pf[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pf)*100:.1f}%)")
    
    # Emails
    colunas_email = [col for col in df_pf.columns if 'email' in col.lower() or 'mail' in col.lower()]
    print(f"Colunas de email encontradas: {colunas_email}")
    
    for col in colunas_email:
        nao_nulos = df_pf[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pf)*100:.1f}%)")
    
    # UF
    colunas_uf = [col for col in df_pf.columns if 'uf' in col.lower()]
    print(f"Colunas de UF encontradas: {colunas_uf}")
    
    for col in colunas_uf:
        nao_nulos = df_pf[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pf)*100:.1f}%)")
    
    return df_pf

def examinar_base_pj():
    """Examina a estrutura da base PJ"""
    print(f"\n" + "="*50)
    print("EXAMINANDO BASE PJ")
    print("="*50)
    
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    
    print(f"Total de registros: {len(df_pj)}")
    print(f"Colunas disponíveis:")
    for i, col in enumerate(df_pj.columns):
        print(f"  {i+1:2d}. {col}")
    
    print(f"\nPrimeiras 3 linhas:")
    print(df_pj.head(3).to_string())
    
    # Verificar dados de contato
    print(f"\nAnálise de dados de contato:")
    
    # Telefones
    colunas_telefone = [col for col in df_pj.columns if 'telefone' in col.lower() or 'fone' in col.lower()]
    print(f"Colunas de telefone encontradas: {colunas_telefone}")
    
    for col in colunas_telefone:
        nao_nulos = df_pj[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pj)*100:.1f}%)")
    
    # Emails
    colunas_email = [col for col in df_pj.columns if 'email' in col.lower() or 'mail' in col.lower()]
    print(f"Colunas de email encontradas: {colunas_email}")
    
    for col in colunas_email:
        nao_nulos = df_pj[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pj)*100:.1f}%)")
    
    # UF
    colunas_uf = [col for col in df_pj.columns if 'uf' in col.lower()]
    print(f"Colunas de UF encontradas: {colunas_uf}")
    
    for col in colunas_uf:
        nao_nulos = df_pj[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pj)*100:.1f}%)")
    
    # CNAE
    colunas_cnae = [col for col in df_pj.columns if 'cnae' in col.lower()]
    print(f"Colunas de CNAE encontradas: {colunas_cnae}")
    
    for col in colunas_cnae:
        nao_nulos = df_pj[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pj)*100:.1f}%)")
    
    # Regime Tributário
    colunas_regime = [col for col in df_pj.columns if 'regime' in col.lower() or 'tributar' in col.lower()]
    print(f"Colunas de Regime Tributário encontradas: {colunas_regime}")
    
    for col in colunas_regime:
        nao_nulos = df_pj[col].notna().sum()
        print(f"  {col}: {nao_nulos} registros preenchidos ({nao_nulos/len(df_pj)*100:.1f}%)")
    
    return df_pj

def examinar_cnaes():
    """Examina o arquivo de CNAEs"""
    print(f"\n" + "="*50)
    print("EXAMINANDO ARQUIVO DE CNAEs")
    print("="*50)
    
    try:
        df_cnaes = pd.read_excel("data/cnaes.xlsx")
        
        print(f"Total de registros: {len(df_cnaes)}")
        print(f"Colunas disponíveis:")
        for i, col in enumerate(df_cnaes.columns):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\nPrimeiras 5 linhas:")
        print(df_cnaes.head().to_string())
        
        print(f"\nTipos de dados:")
        for col, dtype in df_cnaes.dtypes.items():
            print(f"  {col}: {dtype}")
        
        return df_cnaes
        
    except Exception as e:
        print(f"Erro ao carregar arquivo de CNAEs: {e}")
        return None

def testar_matching_cpf_cnpj():
    """Testa o matching entre boletos e bases de clientes"""
    print(f"\n" + "="*50)
    print("TESTANDO MATCHING CPF/CNPJ")
    print("="*50)
    
    # Carregar dados
    df_boletos = pd.read_excel("data/boletos_legalone.xlsx")
    df_pf = pd.read_excel("data/base_pf_legalone.xlsx")
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    
    # Limpar CPF/CNPJ dos boletos
    def limpar_cpf_cnpj(cpf_cnpj):
        if pd.isna(cpf_cnpj):
            return None
        import re
        cpf_cnpj_str = str(cpf_cnpj).strip()
        cpf_cnpj_limpo = re.sub(r'[^\d]', '', cpf_cnpj_str)
        if len(cpf_cnpj_limpo) in [11, 14]:
            return cpf_cnpj_limpo
        return None
    
    # Processar boletos
    df_boletos['cpf_cnpj_limpo'] = df_boletos['Classificações / Destinatário da fatura / CPF/CNPJ'].apply(limpar_cpf_cnpj)
    boletos_com_cpf = df_boletos[df_boletos['cpf_cnpj_limpo'].notna()]
    cpfs_boletos = set(boletos_com_cpf['cpf_cnpj_limpo'].unique())
    
    print(f"CPF/CNPJs únicos nos boletos: {len(cpfs_boletos)}")
    
    # Processar PF
    df_pf['cpf_limpo'] = df_pf['CPF'].apply(limpar_cpf_cnpj)
    cpfs_pf = set(df_pf[df_pf['cpf_limpo'].notna()]['cpf_limpo'].unique())
    
    print(f"CPFs únicos na base PF: {len(cpfs_pf)}")
    
    # Processar PJ
    df_pj['cnpj_limpo'] = df_pj['CNPJ'].apply(limpar_cpf_cnpj)
    cnpjs_pj = set(df_pj[df_pj['cnpj_limpo'].notna()]['cnpj_limpo'].unique())
    
    print(f"CNPJs únicos na base PJ: {len(cnpjs_pj)}")
    
    # Calcular intersecções
    cpfs_bases = cpfs_pf | cnpjs_pj
    intersecao = cpfs_boletos & cpfs_bases
    
    print(f"CPF/CNPJs em comum entre boletos e bases: {len(intersecao)}")
    print(f"Percentual de match: {len(intersecao)/len(cpfs_boletos)*100:.1f}%")
    
    # CPF/CNPJs dos boletos que não estão nas bases
    nao_encontrados = cpfs_boletos - cpfs_bases
    print(f"CPF/CNPJs dos boletos NÃO encontrados nas bases: {len(nao_encontrados)}")
    
    if len(nao_encontrados) > 0:
        print(f"Exemplos de CPF/CNPJs não encontrados:")
        for i, cpf in enumerate(list(nao_encontrados)[:5]):
            nome_boleto = boletos_com_cpf[boletos_com_cpf['cpf_cnpj_limpo'] == cpf]['Classificações / Destinatário da fatura / Nome/razão social'].iloc[0]
            print(f"  {cpf} - {nome_boleto}")

def main():
    df_pf = examinar_base_pf()
    df_pj = examinar_base_pj()
    df_cnaes = examinar_cnaes()
    testar_matching_cpf_cnpj()

if __name__ == "__main__":
    main()
