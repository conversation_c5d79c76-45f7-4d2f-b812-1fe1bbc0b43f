import pandas as pd
import numpy as np

def examinar_arquivo_final():
    """Examina a estrutura do arquivo_final.xlsx"""
    print("EXAMINANDO ARQUIVO_FINAL.XLSX")
    print("="*50)
    
    try:
        # Carregar arquivo
        df = pd.read_excel("data/arquivo_final.xlsx")
        print(f"✓ Arquivo carregado com sucesso!")
        print(f"Dimensões: {df.shape[0]} linhas x {df.shape[1]} colunas")
        
        # Mostrar colunas
        print(f"\nColunas ({len(df.columns)}):")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # Mostrar primeiras linhas
        print(f"\nPrimeiras 5 linhas:")
        print(df.head(5))
        
        # Mostrar tipos de dados
        print(f"\nTipos de dados:")
        print(df.dtypes)
        
        # Verificar valores únicos em algumas colunas importantes
        print(f"\nAnálise de algumas colunas:")
        
        # Verificar se há colunas relacionadas a faturas
        colunas_faturas = [col for col in df.columns if 'fatura' in col.lower()]
        if colunas_faturas:
            print(f"Colunas relacionadas a faturas: {colunas_faturas}")
        
        # Verificar se há colunas relacionadas a boletos
        colunas_boletos = [col for col in df.columns if 'boleto' in col.lower()]
        if colunas_boletos:
            print(f"Colunas relacionadas a boletos: {colunas_boletos}")
        
        # Verificar colunas de valor/financeiras
        colunas_valor = [col for col in df.columns if any(termo in col.lower() for termo in ['valor', 'total', 'receita', 'faturamento'])]
        if colunas_valor:
            print(f"Colunas de valor: {colunas_valor}")
        
        # Verificar colunas de data
        colunas_data = [col for col in df.columns if any(termo in col.lower() for termo in ['data', 'periodo', 'mes', 'ano'])]
        if colunas_data:
            print(f"Colunas de data: {colunas_data}")
        
        # Verificar colunas de identificação
        colunas_id = [col for col in df.columns if any(termo in col.lower() for termo in ['cpf', 'cnpj', 'nome', 'cliente'])]
        if colunas_id:
            print(f"Colunas de identificação: {colunas_id}")
        
        return df
        
    except Exception as e:
        print(f"✗ Erro ao carregar arquivo: {e}")
        return None

def main():
    df = examinar_arquivo_final()
    
    if df is not None:
        # Salvar estrutura em arquivo texto
        with open('teste_novo/estrutura_arquivo_final.txt', 'w', encoding='utf-8') as f:
            f.write("ESTRUTURA DO ARQUIVO_FINAL.XLSX\n")
            f.write("="*50 + "\n\n")
            
            f.write(f"Dimensões: {df.shape[0]} linhas x {df.shape[1]} colunas\n\n")
            
            f.write("COLUNAS:\n")
            for i, col in enumerate(df.columns, 1):
                f.write(f"{i:2d}. {col}\n")
            
            f.write("\nTIPOS DE DADOS:\n")
            for col, dtype in df.dtypes.items():
                f.write(f"{col}: {dtype}\n")
            
            f.write("\nPRIMEIRAS 5 LINHAS:\n")
            f.write(df.head(5).to_string())
        
        print("\n✓ Estrutura salva em: teste_novo/estrutura_arquivo_final.txt")

if __name__ == "__main__":
    main()
