import pandas as pd
import numpy as np

def verificar_cnaes_multiplos():
    """Verifica se há múltiplos CNAEs por empresa na base PJ"""
    print("VERIFICANDO CNAEs MÚLTIPLOS NA BASE PJ")
    print("="*50)
    
    # Carregar base PJ
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    
    print(f"Total de registros: {len(df_pj)}")
    print(f"\nTODAS as colunas da base PJ:")
    for i, col in enumerate(df_pj.columns):
        print(f"  {i+1:2d}. {col}")
    
    # Identificar colunas relacionadas a CNAE
    colunas_cnae = []
    for col in df_pj.columns:
        if 'cnae' in col.lower() or 'atividade' in col.lower():
            colunas_cnae.append(col)
    
    print(f"\nColunas relacionadas a CNAE/Atividade:")
    for col in colunas_cnae:
        print(f"  - {col}")
    
    # Analisar a coluna principal de CNAE
    cnae_col = 'CNAEs / Código'
    if cnae_col in df_pj.columns:
        print(f"\nAnálise da coluna '{cnae_col}':")
        
        # Verificar se há múltiplos CNAEs separados por algum delimitador
        cnaes_com_dados = df_pj[cnae_col].dropna()
        print(f"Total de registros com CNAE: {len(cnaes_com_dados)}")
        
        # Verificar exemplos
        print(f"\nPrimeiros 10 exemplos de CNAEs:")
        for i, cnae in enumerate(cnaes_com_dados.head(10)):
            print(f"  {i+1:2d}. {cnae}")
        
        # Verificar se há CNAEs com múltiplos códigos (separados por vírgula, ponto e vírgula, etc.)
        cnaes_multiplos = []
        delimitadores = [';', ',', '|', '/', ' - ', ' / ']
        
        for cnae in cnaes_com_dados:
            cnae_str = str(cnae).strip()
            for delim in delimitadores:
                if delim in cnae_str:
                    cnaes_multiplos.append(cnae_str)
                    break
        
        print(f"\nCNAEs com múltiplos códigos encontrados: {len(cnaes_multiplos)}")
        if cnaes_multiplos:
            print("Exemplos de CNAEs múltiplos:")
            for i, cnae in enumerate(cnaes_multiplos[:5]):
                print(f"  {i+1}. {cnae}")
        
        # Verificar se há empresas duplicadas (mesmo CNPJ) com CNAEs diferentes
        print(f"\nVerificando empresas com múltiplos registros...")
        cnpjs_duplicados = df_pj[df_pj['CNPJ'].duplicated(keep=False)]['CNPJ'].unique()
        
        if len(cnpjs_duplicados) > 0:
            print(f"Encontrados {len(cnpjs_duplicados)} CNPJs com múltiplos registros")
            
            # Mostrar exemplo
            cnpj_exemplo = cnpjs_duplicados[0]
            registros_exemplo = df_pj[df_pj['CNPJ'] == cnpj_exemplo]
            
            print(f"\nExemplo - CNPJ {cnpj_exemplo}:")
            for idx, row in registros_exemplo.iterrows():
                print(f"  Registro {idx}: {row['Razão social']} - CNAE: {row[cnae_col]}")
        else:
            print("Não há CNPJs duplicados - cada empresa tem apenas um registro")
    
    # Verificar outras possíveis colunas de CNAE
    print(f"\nVerificando outras colunas que podem conter CNAEs...")
    
    # Procurar por padrões de CNAE em outras colunas
    for col in df_pj.columns:
        if col != cnae_col:
            # Verificar se a coluna contém padrões que parecem CNAEs
            valores_nao_nulos = df_pj[col].dropna().astype(str)
            
            # Padrão típico de CNAE: números com pontos e barras (ex: 1234-5/67)
            import re
            padrao_cnae = re.compile(r'\d{4}-\d{1}/\d{2}')
            
            valores_com_cnae = []
            for valor in valores_nao_nulos.head(100):  # Verificar apenas os primeiros 100
                if padrao_cnae.search(valor):
                    valores_com_cnae.append(valor)
            
            if valores_com_cnae:
                print(f"  Coluna '{col}' pode conter CNAEs:")
                for valor in valores_com_cnae[:3]:
                    print(f"    - {valor}")
    
    return df_pj, colunas_cnae

def main():
    df_pj, colunas_cnae = verificar_cnaes_multiplos()
    
    print(f"\n" + "="*50)
    print("CONCLUSÃO:")
    print("="*50)
    
    if len(colunas_cnae) > 1:
        print(f"✓ Encontradas {len(colunas_cnae)} colunas relacionadas a CNAE/Atividade")
        print("  Será necessário consolidar todas as colunas para capturar todos os CNAEs")
    else:
        print(f"✓ Encontrada apenas 1 coluna principal de CNAE")
        print("  Verificar se há múltiplos CNAEs na mesma célula")

if __name__ == "__main__":
    main()
