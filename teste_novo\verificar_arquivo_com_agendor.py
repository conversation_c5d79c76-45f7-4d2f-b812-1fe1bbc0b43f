import pandas as pd

def verificar_arquivo_com_agendor():
    """Verifica o arquivo final com flag LegalOne e busca no Agendor"""
    print("VERIFICANDO ARQUIVO FINAL COM AGENDOR E FLAG LEGALONE")
    print("="*60)
    
    try:
        df = pd.read_excel("teste_novo/arquivo_final_boletos_com_agendor.xlsx")
        
        print("✓ Arquivo carregado com sucesso!")
        print(f"Total de registros: {len(df)}")
        
        print(f"\n📋 COLUNAS DO ARQUIVO:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # Verificar flag Cliente_LegalOne
        print(f"\n🏢 ANÁLISE FLAG CLIENTE_LEGALONE:")
        if 'Cliente_LegalOne' in df.columns:
            legalone_sim = len(df[df['Cliente_LegalOne'] == 'SIM'])
            legalone_nao = len(df[df['Cliente_LegalOne'] == 'NAO'])
            
            print(f"  ✅ Coluna 'Cliente_LegalOne' encontrada!")
            print(f"  - Clientes SIM (no LegalOne): {legalone_sim}")
            print(f"  - Clientes NAO (não no LegalOne): {legalone_nao}")
            print(f"  - Total: {legalone_sim + legalone_nao}")
            
            # Mostrar exemplos de clientes NAO
            if legalone_nao > 0:
                print(f"\n  📋 CLIENTES NÃO ENCONTRADOS NO LEGALONE:")
                clientes_nao = df[df['Cliente_LegalOne'] == 'NAO'][['Nome', 'CPF_CNPJ', 'Tipo_Destinatario', 'Telefones', 'Emails', 'UF']]
                for idx, row in clientes_nao.iterrows():
                    nome = row['Nome'][:40] if row['Nome'] else 'N/A'
                    cpf_cnpj = row['CPF_CNPJ']
                    tipo = row['Tipo_Destinatario']
                    telefone = row['Telefones'] if pd.notna(row['Telefones']) else 'N/A'
                    email = row['Emails'] if pd.notna(row['Emails']) else 'N/A'
                    uf = row['UF'] if pd.notna(row['UF']) else 'N/A'
                    
                    print(f"    • {nome:<40} | {tipo} | {cpf_cnpj}")
                    if telefone != 'N/A' or email != 'N/A' or uf != 'N/A':
                        print(f"      Dados complementares: Tel: {telefone} | Email: {email} | UF: {uf}")
        else:
            print(f"  ❌ Coluna 'Cliente_LegalOne' NÃO encontrada!")
        
        # Verificar múltiplos CNAEs
        print(f"\n🏭 ANÁLISE MÚLTIPLOS CNAEs:")
        if 'CNAE' in df.columns:
            clientes_multiplos_cnaes = df[df['CNAE'].str.contains(';', na=False)]
            print(f"  ✅ {len(clientes_multiplos_cnaes)} clientes com múltiplos CNAEs")
            
            if len(clientes_multiplos_cnaes) > 0:
                print(f"\n  📋 EXEMPLOS DE MÚLTIPLOS CNAEs:")
                for idx, row in clientes_multiplos_cnaes.head(5).iterrows():
                    nome = row['Nome'][:35] if row['Nome'] else 'N/A'
                    cnaes = row['CNAE']
                    qtd_cnaes = len(cnaes.split(';'))
                    primeiro_cnae = row['Primeiro_CNAE'] if pd.notna(row['Primeiro_CNAE']) else 'N/A'
                    descricao = row['Descricao_CNAE'][:50] if pd.notna(row['Descricao_CNAE']) else 'N/A'
                    
                    print(f"    • {nome:<35} | {qtd_cnaes} CNAEs")
                    print(f"      Primeiro: {primeiro_cnae} | {descricao}...")
                    print(f"      Todos: {cnaes[:80]}{'...' if len(cnaes) > 80 else ''}")
                    print()
        
        # Verificar formato das datas
        print(f"\n📅 ANÁLISE FORMATO DAS DATAS:")
        colunas_data = ['Data_Primeiro_Boleto', 'Data_Ultimo_Boleto']
        
        for coluna in colunas_data:
            if coluna in df.columns:
                exemplos_data = df[coluna].dropna().head(3)
                print(f"  {coluna}:")
                for i, data in enumerate(exemplos_data):
                    print(f"    {i+1}. {data}")
        
        # Verificar valores numéricos
        print(f"\n💰 ANÁLISE VALORES NUMÉRICOS:")
        colunas_valor = ['Valor_Total_Recebido', 'Valor_Medio_Por_Boleto']
        
        for coluna in colunas_valor:
            if coluna in df.columns:
                exemplo_valor = df[coluna].dropna().iloc[0]
                print(f"  {coluna}: {exemplo_valor} (tipo: {type(exemplo_valor)})")
        
        # Estatísticas gerais
        print(f"\n📊 ESTATÍSTICAS GERAIS:")
        print(f"  - Total de registros: {len(df)}")
        print(f"  - Clientes PF: {len(df[df['Tipo_Destinatario'] == 'PF'])}")
        print(f"  - Clientes PJ: {len(df[df['Tipo_Destinatario'] == 'PJ'])}")
        print(f"  - Com telefone: {df['Telefones'].notna().sum()}")
        print(f"  - Com email: {df['Emails'].notna().sum()}")
        print(f"  - Com CNAE: {df['CNAE'].notna().sum()}")
        print(f"  - Com UF: {df['UF'].notna().sum()}")
        
        # Verificar se há dados do Agendor
        print(f"\n🔍 VERIFICAÇÃO DADOS DO AGENDOR:")
        clientes_nao_legalone = df[df['Cliente_LegalOne'] == 'NAO']
        
        if len(clientes_nao_legalone) > 0:
            clientes_com_dados_extras = clientes_nao_legalone[
                (clientes_nao_legalone['Telefones'].notna()) | 
                (clientes_nao_legalone['Emails'].notna()) | 
                (clientes_nao_legalone['UF'].notna())
            ]
            
            print(f"  - Clientes NÃO LegalOne: {len(clientes_nao_legalone)}")
            print(f"  - Com dados complementares: {len(clientes_com_dados_extras)}")
            
            if len(clientes_com_dados_extras) > 0:
                print(f"  ✅ Dados do Agendor foram utilizados com sucesso!")
            else:
                print(f"  ⚠️ Nenhum cliente teve dados complementados pelo Agendor")
        
        return df
        
    except Exception as e:
        print(f"✗ Erro: {e}")
        return None

def main():
    df = verificar_arquivo_com_agendor()
    
    if df is not None:
        print(f"\n" + "="*60)
        print("✅ VERIFICAÇÃO CONCLUÍDA COM SUCESSO!")
        print("="*60)
        print(f"📁 Arquivo: teste_novo/arquivo_final_boletos_com_agendor.xlsx")
        print(f"\n🎯 ALTERAÇÕES IMPLEMENTADAS:")
        print(f"   ✅ Coluna 'Cliente_LegalOne' com flag SIM/NAO")
        print(f"   ✅ Busca complementar no database Agendor")
        print(f"   ✅ TODOS os CNAEs capturados (separados por ;)")
        print(f"   ✅ Datas formatadas como DD/MM/AAAA")
        print(f"   ✅ Valores numéricos com 2 casas decimais")
        print(f"   ✅ Nomes corretos (BOLETOS, não faturas)")

if __name__ == "__main__":
    main()
