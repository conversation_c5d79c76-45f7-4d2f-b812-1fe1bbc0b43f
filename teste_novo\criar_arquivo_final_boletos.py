import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def limpar_cpf_cnpj(cpf_cnpj):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(cpf_cnpj):
        return None
    
    # Converter para string e remover caracteres especiais
    cpf_cnpj_str = str(cpf_cnpj).strip()
    cpf_cnpj_limpo = re.sub(r'[^\d]', '', cpf_cnpj_str)
    
    # Verificar se tem tamanho válido
    if len(cpf_cnpj_limpo) == 11:  # CPF
        return cpf_cnpj_limpo
    elif len(cpf_cnpj_limpo) == 14:  # CNPJ
        return cpf_cnpj_limpo
    else:
        return None

def limpar_nome(nome):
    """Limpa e normaliza nomes para matching"""
    if pd.isna(nome):
        return ""
    
    nome_str = str(nome).upper().strip()
    
    # Remover termos comuns de empresas
    termos_remover = [
        'LTDA', 'LIMITADA', 'S/A', 'S.A.', 'SA', 'SOCIEDADE ANONIMA',
        'EIRELI', 'ME', 'EPP', 'MICROEMPRESA', 'EMPRESA INDIVIDUAL',
        'CIA', 'COMPANHIA', '&', 'E', 'SERVICOS', 'COMERCIO',
        'INDUSTRIA', 'CONSULTORIA', 'ASSESSORIA'
    ]
    
    for termo in termos_remover:
        nome_str = re.sub(r'\b' + termo + r'\b', '', nome_str)
    
    # Remover caracteres especiais e espaços extras
    nome_str = re.sub(r'[^\w\s]', ' ', nome_str)
    nome_str = re.sub(r'\s+', ' ', nome_str).strip()
    
    return nome_str

def carregar_dados():
    """Carrega todos os dados necessários"""
    print("Carregando dados...")
    
    # Carregar boletos
    print("- Carregando boletos...")
    df_boletos = pd.read_excel("data/boletos_legalone.xlsx")
    print(f"  ✓ {len(df_boletos)} registros de boletos")
    
    # Carregar base PF
    print("- Carregando base PF...")
    df_pf = pd.read_excel("data/base_pf_legalone.xlsx")
    print(f"  ✓ {len(df_pf)} registros PF")
    
    # Carregar base PJ
    print("- Carregando base PJ...")
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    print(f"  ✓ {len(df_pj)} registros PJ")
    
    return df_boletos, df_pf, df_pj

def processar_boletos(df_boletos):
    """Processa dados dos boletos"""
    print("\nProcessando boletos...")
    
    # Renomear colunas para facilitar
    df_boletos = df_boletos.rename(columns={
        'Movimentos / Recebido em': 'data_recebimento',
        'Valor líquido realizado': 'valor',
        'Classificações / Destinatário da fatura / Nome/razão social': 'nome',
        'Classificações / Destinatário da fatura / CPF/CNPJ': 'cpf_cnpj'
    })
    
    # Limpar CPF/CNPJ
    df_boletos['cpf_cnpj_limpo'] = df_boletos['cpf_cnpj'].apply(limpar_cpf_cnpj)
    df_boletos['nome_limpo'] = df_boletos['nome'].apply(limpar_nome)
    
    # Remover registros sem CPF/CNPJ válido
    df_boletos_validos = df_boletos[df_boletos['cpf_cnpj_limpo'].notna()].copy()
    print(f"  ✓ {len(df_boletos_validos)} boletos com CPF/CNPJ válido")
    
    return df_boletos_validos

def processar_bases_clientes(df_pf, df_pj):
    """Processa bases de clientes PF e PJ"""
    print("\nProcessando bases de clientes...")
    
    # Processar PF
    df_pf_proc = df_pf.copy()
    df_pf_proc['cpf_cnpj_limpo'] = df_pf_proc['CPF'].apply(limpar_cpf_cnpj)
    df_pf_proc['tipo'] = 'PF'
    df_pf_proc = df_pf_proc.rename(columns={
        'Nome': 'nome',
        'Endereços / UF': 'uf',
        'E-mails / E-mail': 'email',
        'Telefones / Número': 'telefone'
    })
    
    # Processar PJ
    df_pj_proc = df_pj.copy()
    df_pj_proc['cpf_cnpj_limpo'] = df_pj_proc['CNPJ'].apply(limpar_cpf_cnpj)
    df_pj_proc['tipo'] = 'PJ'
    df_pj_proc = df_pj_proc.rename(columns={
        'Razão social': 'nome',
        'Endereços / UF': 'uf',
        'E-mails / E-mail': 'email',
        'Telefones / Número': 'telefone',
        'CNAEs / Código': 'cnae',
        'Regime Tributário': 'regime_tributario'
    })
    
    # Combinar bases
    colunas_comuns = ['nome', 'cpf_cnpj_limpo', 'tipo', 'uf', 'email', 'telefone']
    colunas_pj_extras = ['cnae', 'regime_tributario']
    
    # Adicionar colunas extras para PF (preenchidas com NaN)
    for col in colunas_pj_extras:
        df_pf_proc[col] = np.nan
    
    # Selecionar colunas
    df_pf_final = df_pf_proc[colunas_comuns + colunas_pj_extras]
    df_pj_final = df_pj_proc[colunas_comuns + colunas_pj_extras]
    
    # Combinar
    df_clientes = pd.concat([df_pf_final, df_pj_final], ignore_index=True)
    
    # Remover duplicatas e registros sem CPF/CNPJ
    df_clientes = df_clientes[df_clientes['cpf_cnpj_limpo'].notna()]
    df_clientes = df_clientes.drop_duplicates(subset=['cpf_cnpj_limpo'])
    
    print(f"  ✓ {len(df_clientes)} clientes únicos processados")
    
    return df_clientes

def calcular_metricas_boletos(df_boletos_validos):
    """Calcula métricas dos boletos por cliente"""
    print("\nCalculando métricas dos boletos...")
    
    # Agrupar por CPF/CNPJ
    metricas = []
    
    for cpf_cnpj, grupo in df_boletos_validos.groupby('cpf_cnpj_limpo'):
        # Ordenar por data
        grupo_ordenado = grupo.sort_values('data_recebimento')
        
        # Calcular métricas
        data_primeiro_boleto = grupo_ordenado['data_recebimento'].min()
        data_ultimo_boleto = grupo_ordenado['data_recebimento'].max()
        
        # Calcular meses desde último boleto
        hoje = datetime.now()
        meses_desde_ultimo = ((hoje.year - data_ultimo_boleto.year) * 12 + 
                             (hoje.month - data_ultimo_boleto.month))
        
        # Calcular período cliente em meses (adicionar 1 conforme preferência do usuário)
        periodo_meses = ((data_ultimo_boleto.year - data_primeiro_boleto.year) * 12 + 
                        (data_ultimo_boleto.month - data_primeiro_boleto.month)) + 1
        
        # Métricas de valor
        total_boletos = len(grupo)
        valor_total = grupo['valor'].sum()
        valor_medio = grupo['valor'].mean()
        valor_minimo = grupo['valor'].min()
        valor_maximo = grupo['valor'].max()
        
        # Frequência (boletos por mês)
        frequencia_por_mes = total_boletos / max(periodo_meses, 1)
        
        # Nome (pegar o mais comum)
        nome_mais_comum = grupo['nome'].mode().iloc[0] if not grupo['nome'].mode().empty else grupo['nome'].iloc[0]
        
        metricas.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome': nome_mais_comum,
            'data_primeiro_boleto': data_primeiro_boleto,
            'data_ultimo_boleto': data_ultimo_boleto,
            'meses_desde_ultimo_boleto': meses_desde_ultimo,
            'periodo_cliente_meses': periodo_meses,
            'total_boletos': total_boletos,
            'valor_total': valor_total,
            'valor_medio': valor_medio,
            'valor_minimo': valor_minimo,
            'valor_maximo': valor_maximo,
            'frequencia_por_mes': frequencia_por_mes
        })
    
    df_metricas = pd.DataFrame(metricas)
    print(f"  ✓ Métricas calculadas para {len(df_metricas)} clientes")
    
    return df_metricas

def consolidar_dados_cliente(df_clientes):
    """Consolida dados de clientes (telefones, emails, etc.)"""
    print("\nConsolidando dados de clientes...")

    # Agrupar por CPF/CNPJ e consolidar informações
    clientes_consolidados = []

    for cpf_cnpj, grupo in df_clientes.groupby('cpf_cnpj_limpo'):
        # Pegar primeiro registro não-nulo para cada campo
        nome = grupo['nome'].dropna().iloc[0] if not grupo['nome'].dropna().empty else None
        tipo = grupo['tipo'].dropna().iloc[0] if not grupo['tipo'].dropna().empty else None
        uf = grupo['uf'].dropna().iloc[0] if not grupo['uf'].dropna().empty else None
        regime_tributario = grupo['regime_tributario'].dropna().iloc[0] if not grupo['regime_tributario'].dropna().empty else None

        # Consolidar telefones (remover duplicatas)
        telefones = grupo['telefone'].dropna().unique()
        telefones_str = '; '.join([str(t) for t in telefones if str(t) != 'nan']) if len(telefones) > 0 else None

        # Consolidar emails (remover duplicatas)
        emails = grupo['email'].dropna().unique()
        emails_str = '; '.join([str(e) for e in emails if str(e) != 'nan']) if len(emails) > 0 else None

        # Consolidar CNAEs
        cnaes = grupo['cnae'].dropna().unique()
        cnae_principal = cnaes[0] if len(cnaes) > 0 else None
        cnaes_str = '; '.join([str(c) for c in cnaes if str(c) != 'nan']) if len(cnaes) > 0 else None

        clientes_consolidados.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome_cliente': nome,
            'tipo_destinatario': tipo,
            'uf': uf,
            'telefones': telefones_str,
            'emails': emails_str,
            'cnae': cnaes_str,
            'primeiro_cnae': cnae_principal,
            'regime_tributario': regime_tributario
        })

    df_consolidado = pd.DataFrame(clientes_consolidados)
    print(f"  ✓ {len(df_consolidado)} clientes consolidados")

    return df_consolidado

def formatar_arquivo_final(df_metricas, df_clientes_consolidado):
    """Formata dados no mesmo formato do arquivo_final.xlsx"""
    print("\nFormatando arquivo final...")

    # Fazer merge
    df_final = df_metricas.merge(df_clientes_consolidado, on='cpf_cnpj_limpo', how='left')

    # Criar DataFrame no formato do arquivo_final.xlsx
    df_formatado = pd.DataFrame()

    # Mapear colunas
    df_formatado['Nome'] = df_final['nome_cliente'].fillna(df_final['nome'])
    df_formatado['CPF_CNPJ'] = df_final['cpf_cnpj_limpo'].astype(float)
    df_formatado['Tipo_Destinatario'] = df_final['tipo_destinatario']
    df_formatado['Meses_Desde_Ultima_Fatura'] = df_final['meses_desde_ultimo_boleto']  # Substituindo "Fatura" por dados de boleto
    df_formatado['Data_Ultima_Fatura'] = df_final['data_ultimo_boleto']  # Substituindo "Fatura" por dados de boleto
    df_formatado['Data_Primeira_Fatura'] = df_final['data_primeiro_boleto']  # Substituindo "Fatura" por dados de boleto
    df_formatado['Periodo_Cliente_Meses'] = df_final['periodo_cliente_meses']
    df_formatado['Total_Faturas_Unicas'] = df_final['total_boletos']  # Substituindo "Faturas" por boletos
    df_formatado['Valor_Total_Faturado'] = df_final['valor_total'].round(2)  # Substituindo "Faturado" por recebido via boletos
    df_formatado['Valor_Medio_Por_Fatura'] = df_final['valor_medio'].round(2)  # Substituindo "Fatura" por boleto
    df_formatado['Valor_Minimo'] = df_final['valor_minimo'].round(2)
    df_formatado['Valor_Maximo'] = df_final['valor_maximo'].round(2)
    df_formatado['Frequencia_Faturas_Por_Mes'] = df_final['frequencia_por_mes'].round(2)  # Substituindo "Faturas" por boletos
    df_formatado['Telefones'] = df_final['telefones']
    df_formatado['Emails'] = df_final['emails']
    df_formatado['UF'] = df_final['uf']
    df_formatado['CNAE'] = df_final['cnae']
    df_formatado['Regime_Tributario'] = df_final['regime_tributario']
    df_formatado['Primeiro_CNAE'] = df_final['primeiro_cnae']
    df_formatado['Descricao_CNAE'] = np.nan  # Não temos descrição CNAE nos dados fonte

    print(f"  ✓ Arquivo formatado com {len(df_formatado)} registros")

    return df_formatado

def salvar_arquivo(df_formatado):
    """Salva o arquivo final"""
    print("\nSalvando arquivo...")

    nome_arquivo = "teste_novo/arquivo_final_boletos.xlsx"
    df_formatado.to_excel(nome_arquivo, index=False)

    print(f"  ✓ Arquivo salvo: {nome_arquivo}")

    # Mostrar resumo
    print(f"\nResumo do arquivo gerado:")
    print(f"- Total de registros: {len(df_formatado)}")
    print(f"- Clientes PF: {len(df_formatado[df_formatado['Tipo_Destinatario'] == 'PF'])}")
    print(f"- Clientes PJ: {len(df_formatado[df_formatado['Tipo_Destinatario'] == 'PJ'])}")
    print(f"- Valor total recebido: R$ {df_formatado['Valor_Total_Faturado'].sum():,.2f}")
    print(f"- Período de dados: {df_formatado['Data_Primeira_Fatura'].min()} a {df_formatado['Data_Ultima_Fatura'].max()}")

def main():
    print("CRIANDO ARQUIVO FINAL COM DADOS DOS BOLETOS")
    print("="*60)

    # Carregar dados
    df_boletos, df_pf, df_pj = carregar_dados()

    # Processar boletos
    df_boletos_proc = processar_boletos(df_boletos)

    # Processar bases de clientes
    df_clientes = processar_bases_clientes(df_pf, df_pj)

    # Consolidar dados de clientes
    df_clientes_consolidado = consolidar_dados_cliente(df_clientes)

    # Calcular métricas dos boletos
    df_metricas = calcular_metricas_boletos(df_boletos_proc)

    # Formatar arquivo final
    df_formatado = formatar_arquivo_final(df_metricas, df_clientes_consolidado)

    # Salvar arquivo
    salvar_arquivo(df_formatado)

    return df_formatado

if __name__ == "__main__":
    df_resultado = main()
