import pandas as pd
import sys
import os

def examinar_arquivo(caminho, nome):
    """Examina a estrutura básica de um arquivo Excel"""
    print(f"\n{'='*50}")
    print(f"EXAMINANDO: {nome}")
    print(f"Caminho: {caminho}")
    print(f"{'='*50}")
    
    try:
        # Verificar se arquivo existe
        if not os.path.exists(caminho):
            print(f"✗ Arquivo não encontrado: {caminho}")
            return None
            
        # Carregar arquivo
        df = pd.read_excel(caminho)
        print(f"✓ Arquivo carregado com sucesso!")
        print(f"Dimensões: {df.shape[0]} linhas x {df.shape[1]} colunas")
        
        # Mostrar colunas
        print(f"\nColunas ({len(df.columns)}):")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # Mostrar primeiras linhas
        print(f"\nPrimeiras 3 linhas:")
        print(df.head(3))
        
        return df
        
    except Exception as e:
        print(f"✗ Erro ao carregar arquivo: {e}")
        return None

def main():
    print("EXAMINANDO ESTRUTURA DOS ARQUIVOS FINANCEIROS")
    print("="*60)
    
    # Caminhos dos arquivos
    caminho_boletos = "data/boletos_legalone.xlsx"
    caminho_faturas = "data/faturas_emitidas.xlsx"
    
    # Examinar boletos
    boletos = examinar_arquivo(caminho_boletos, "BOLETOS")
    
    # Examinar faturas  
    faturas = examinar_arquivo(caminho_faturas, "FATURAS")
    
    print(f"\n{'='*60}")
    print("EXAME CONCLUÍDO!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
