import pandas as pd
import numpy as np
import re
from datetime import datetime

def limpar_cpf_cnpj(valor):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(valor):
        return None
    
    valor_str = str(valor).strip()
    valor_limpo = ''.join(filter(str.isdigit, valor_str))
    
    if len(valor_limpo) in [11, 14]:
        return valor_limpo
    
    return None

def limpar_nome(nome):
    """Limpa e padroniza nomes para comparação"""
    if pd.isna(nome):
        return ""
    
    nome_str = str(nome).upper().strip()
    nome_str = re.sub(r'[^\w\s]', ' ', nome_str)
    
    palavras_remover = ['LTDA', 'LTD', 'S/A', 'SA', 'EIRELI', 'EPP', 'ME', 'SOCIEDADE', 'CIA', 'COMPANHIA']
    for palavra in palavras_remover:
        nome_str = re.sub(rf'\b{palavra}\b', '', nome_str)
    
    nome_str = re.sub(r'\s+', ' ', nome_str).strip()
    return nome_str

def criar_mapeamento_nome_cpf(boletos):
    """Cria mapeamento de nome limpo para CPF/CNPJ"""
    mapeamento = {}
    
    for _, row in boletos.iterrows():
        nome = row['nome_cliente']
        cpf_cnpj = row['cpf_cnpj']
        
        if pd.notna(nome) and pd.notna(cpf_cnpj):
            nome_limpo = limpar_nome(nome)
            cpf_cnpj_limpo = limpar_cpf_cnpj(cpf_cnpj)
            
            if nome_limpo.strip() != "" and cpf_cnpj_limpo:
                if nome_limpo not in mapeamento:
                    mapeamento[nome_limpo] = set()
                mapeamento[nome_limpo].add(cpf_cnpj_limpo)
    
    # Converter sets para listas para facilitar uso
    for nome in mapeamento:
        mapeamento[nome] = list(mapeamento[nome])
    
    return mapeamento

def mapear_faturas_para_cpf(faturas, mapeamento_nome_cpf):
    """Mapeia faturas para CPF/CNPJ usando nomes"""
    faturas_mapeadas = faturas.copy()
    faturas_mapeadas['cpf_cnpj_mapeado'] = None
    
    for idx, row in faturas_mapeadas.iterrows():
        nome_fatura = row['destinatario']
        nome_limpo = limpar_nome(nome_fatura)
        
        if nome_limpo in mapeamento_nome_cpf:
            # Se há múltiplos CPF/CNPJ para o mesmo nome, pegar o primeiro
            cpf_cnpj = mapeamento_nome_cpf[nome_limpo][0]
            faturas_mapeadas.at[idx, 'cpf_cnpj_mapeado'] = cpf_cnpj
    
    return faturas_mapeadas

def analisar_dados_completos():
    """Análise completa dos dados financeiros"""
    print("ANÁLISE FINANCEIRA COMPLETA - BOLETOS vs FATURAS")
    print("="*70)
    
    # Carregar dados
    print("Carregando dados...")
    boletos = pd.read_excel("data/boletos_legalone.xlsx")
    boletos.columns = ['data_recebimento', 'valor_liquido', 'nome_cliente', 'cpf_cnpj']
    
    faturas = pd.read_excel("data/faturas_emitidas.xlsx")
    faturas.columns = ['status', 'data_emissao', 'numero_fatura', 'agrupado_por', 'destinatario', 'tipo', 'valor']
    
    print(f"✓ Boletos carregados: {len(boletos):,} registros")
    print(f"✓ Faturas carregadas: {len(faturas):,} registros")
    
    # Processar boletos
    print(f"\n{'='*50}")
    print("ANÁLISE DOS BOLETOS")
    print("="*50)
    
    boletos['cpf_cnpj_limpo'] = boletos['cpf_cnpj'].apply(limpar_cpf_cnpj)
    boletos_validos = boletos.dropna(subset=['cpf_cnpj_limpo']).copy()
    
    print(f"Registros com CPF/CNPJ válido: {len(boletos_validos):,}")
    print(f"Clientes únicos: {boletos_validos['cpf_cnpj_limpo'].nunique():,}")
    
    # Métricas de valores dos boletos
    valores_boletos = boletos_validos['valor_liquido'].dropna()
    print(f"Total recebido: R$ {valores_boletos.sum():,.2f}")
    print(f"Valor médio por boleto: R$ {valores_boletos.mean():,.2f}")
    print(f"Valor mediano: R$ {valores_boletos.median():,.2f}")
    
    # Criar mapeamento nome -> CPF/CNPJ
    print(f"\nCriando mapeamento nome -> CPF/CNPJ...")
    mapeamento_nome_cpf = criar_mapeamento_nome_cpf(boletos_validos)
    print(f"✓ {len(mapeamento_nome_cpf):,} nomes mapeados")
    
    # Processar faturas
    print(f"\n{'='*50}")
    print("ANÁLISE DAS FATURAS")
    print("="*50)
    
    # Mapear faturas para CPF/CNPJ
    faturas_mapeadas = mapear_faturas_para_cpf(faturas, mapeamento_nome_cpf)
    faturas_validas = faturas_mapeadas.dropna(subset=['cpf_cnpj_mapeado']).copy()
    
    print(f"Faturas mapeadas para CPF/CNPJ: {len(faturas_validas):,}")
    print(f"Faturas não mapeadas: {len(faturas) - len(faturas_validas):,}")
    print(f"Clientes únicos nas faturas: {faturas_validas['cpf_cnpj_mapeado'].nunique():,}")
    
    # Métricas de valores das faturas
    valores_faturas = faturas_validas['valor'].dropna()
    print(f"Total faturado: R$ {valores_faturas.sum():,.2f}")
    print(f"Valor médio por fatura: R$ {valores_faturas.mean():,.2f}")
    print(f"Valor mediano: R$ {valores_faturas.median():,.2f}")
    
    # Análise por status das faturas
    print(f"\nFaturas por status:")
    status_faturas = faturas_validas.groupby('status').agg({
        'valor': ['count', 'sum']
    }).round(2)
    
    for status, dados in status_faturas.iterrows():
        qtd = dados[('valor', 'count')]
        total = dados[('valor', 'sum')]
        print(f"  {status}: {qtd} faturas - R$ {total:,.2f}")
    
    # Comparação entre bases
    print(f"\n{'='*60}")
    print("COMPARAÇÃO ENTRE BOLETOS E FATURAS")
    print("="*60)
    
    cpfs_boletos = set(boletos_validos['cpf_cnpj_limpo'].unique())
    cpfs_faturas = set(faturas_validas['cpf_cnpj_mapeado'].unique())
    
    intersecao = cpfs_boletos.intersection(cpfs_faturas)
    apenas_boletos = cpfs_boletos - cpfs_faturas
    apenas_faturas = cpfs_faturas - cpfs_boletos
    total_unicos = len(cpfs_boletos.union(cpfs_faturas))
    
    print(f"Clientes únicos em BOLETOS: {len(cpfs_boletos):,}")
    print(f"Clientes únicos em FATURAS: {len(cpfs_faturas):,}")
    print(f"Clientes em AMBAS as bases: {len(intersecao):,}")
    print(f"Clientes APENAS em boletos: {len(apenas_boletos):,}")
    print(f"Clientes APENAS em faturas: {len(apenas_faturas):,}")
    print(f"Total de clientes únicos: {total_unicos:,}")
    
    if total_unicos > 0:
        print(f"\nPercentuais:")
        print(f"  Sobreposição: {len(intersecao)/total_unicos*100:.1f}%")
        print(f"  Exclusivos de boletos: {len(apenas_boletos)/total_unicos*100:.1f}%")
        print(f"  Exclusivos de faturas: {len(apenas_faturas)/total_unicos*100:.1f}%")
    
    # Análise dos clientes em comum
    if len(intersecao) > 0:
        print(f"\n{'='*50}")
        print(f"ANÁLISE DOS CLIENTES EM COMUM ({len(intersecao):,} clientes)")
        print("="*50)
        
        boletos_comum = boletos_validos[boletos_validos['cpf_cnpj_limpo'].isin(intersecao)]
        faturas_comum = faturas_validas[faturas_validas['cpf_cnpj_mapeado'].isin(intersecao)]
        
        total_recebido_comum = boletos_comum['valor_liquido'].sum()
        total_faturado_comum = faturas_comum['valor'].sum()
        
        print(f"Total recebido (boletos): R$ {total_recebido_comum:,.2f}")
        print(f"Total faturado (faturas): R$ {total_faturado_comum:,.2f}")
        
        if total_faturado_comum > 0:
            taxa_recebimento = (total_recebido_comum / total_faturado_comum) * 100
            print(f"Taxa de recebimento: {taxa_recebimento:.1f}%")
        
        # Top clientes em comum por valor
        por_cliente_comum = boletos_comum.groupby('cpf_cnpj_limpo').agg({
            'valor_liquido': 'sum'
        }).round(2)
        
        top_comum = por_cliente_comum.sort_values('valor_liquido', ascending=False).head(10)
        print(f"\nTop 10 clientes em comum por valor recebido:")
        for i, (cpf_cnpj, dados) in enumerate(top_comum.iterrows(), 1):
            print(f"{i:2d}. {cpf_cnpj}: R$ {dados['valor_liquido']:,.2f}")
    
    # Análise temporal
    print(f"\n{'='*50}")
    print("ANÁLISE TEMPORAL")
    print("="*50)
    
    # Converter datas
    boletos_validos['data_recebimento'] = pd.to_datetime(boletos_validos['data_recebimento'])
    faturas_validas['data_emissao'] = pd.to_datetime(faturas_validas['data_emissao'], format='%d/%m/%Y')
    
    # Análise por ano - boletos
    boletos_por_ano = boletos_validos.groupby(boletos_validos['data_recebimento'].dt.year).agg({
        'valor_liquido': 'sum',
        'cpf_cnpj_limpo': 'nunique'
    }).round(2)
    
    print(f"Recebimento por ano (boletos):")
    for ano, dados in boletos_por_ano.iterrows():
        if not pd.isna(ano):
            print(f"  {int(ano)}: R$ {dados['valor_liquido']:,.2f} ({dados['cpf_cnpj_limpo']} clientes)")
    
    # Análise por ano - faturas
    faturas_por_ano = faturas_validas.groupby(faturas_validas['data_emissao'].dt.year).agg({
        'valor': 'sum',
        'cpf_cnpj_mapeado': 'nunique'
    }).round(2)
    
    print(f"\nFaturamento por ano (faturas):")
    for ano, dados in faturas_por_ano.iterrows():
        if not pd.isna(ano):
            print(f"  {int(ano)}: R$ {dados['valor']:,.2f} ({dados['cpf_cnpj_mapeado']} clientes)")
    
    # Resumo executivo
    print(f"\n{'='*70}")
    print("RESUMO EXECUTIVO")
    print("="*70)
    
    total_recebido = boletos_validos['valor_liquido'].sum()
    total_faturado = faturas_validas['valor'].sum()
    
    print(f"📊 DADOS GERAIS:")
    print(f"  • Total de registros de boletos: {len(boletos_validos):,}")
    print(f"  • Total de registros de faturas: {len(faturas_validas):,}")
    print(f"  • Clientes únicos em boletos: {len(cpfs_boletos):,}")
    print(f"  • Clientes únicos em faturas: {len(cpfs_faturas):,}")
    print(f"  • Total de clientes únicos: {total_unicos:,}")
    
    print(f"\n💰 VALORES:")
    print(f"  • Total recebido (boletos): R$ {total_recebido:,.2f}")
    print(f"  • Total faturado (faturas): R$ {total_faturado:,.2f}")
    
    print(f"\n🔗 RELACIONAMENTO:")
    print(f"  • Clientes em ambas as bases: {len(intersecao):,} ({len(intersecao)/total_unicos*100:.1f}%)")
    print(f"  • Clientes apenas em boletos: {len(apenas_boletos):,} ({len(apenas_boletos)/total_unicos*100:.1f}%)")
    print(f"  • Clientes apenas em faturas: {len(apenas_faturas):,} ({len(apenas_faturas)/total_unicos*100:.1f}%)")
    
    if len(intersecao) > 0 and total_faturado_comum > 0:
        print(f"  • Taxa de recebimento (clientes comuns): {taxa_recebimento:.1f}%")
    
    print(f"\n{'='*70}")
    print("ANÁLISE CONCLUÍDA!")
    print("="*70)
    
    return {
        'boletos_validos': boletos_validos,
        'faturas_validas': faturas_validas,
        'intersecao': intersecao,
        'apenas_boletos': apenas_boletos,
        'apenas_faturas': apenas_faturas,
        'mapeamento_nome_cpf': mapeamento_nome_cpf
    }

def main():
    resultado = analisar_dados_completos()
    
    # Salvar alguns dados para análise posterior se necessário
    print(f"\nSalvando dados processados...")
    
    # Salvar lista de clientes únicos
    with open('teste_novo/clientes_unicos_boletos.txt', 'w', encoding='utf-8') as f:
        f.write("CPF/CNPJ dos clientes únicos em BOLETOS:\n")
        f.write("="*50 + "\n")
        for cpf in sorted(resultado['boletos_validos']['cpf_cnpj_limpo'].unique()):
            f.write(f"{cpf}\n")
    
    with open('teste_novo/clientes_unicos_faturas.txt', 'w', encoding='utf-8') as f:
        f.write("CPF/CNPJ dos clientes únicos em FATURAS:\n")
        f.write("="*50 + "\n")
        for cpf in sorted(resultado['faturas_validas']['cpf_cnpj_mapeado'].unique()):
            f.write(f"{cpf}\n")
    
    with open('teste_novo/clientes_em_comum.txt', 'w', encoding='utf-8') as f:
        f.write("CPF/CNPJ dos clientes presentes em AMBAS as bases:\n")
        f.write("="*50 + "\n")
        for cpf in sorted(resultado['intersecao']):
            f.write(f"{cpf}\n")
    
    print("✓ Arquivos salvos em teste_novo/")

if __name__ == "__main__":
    main()
