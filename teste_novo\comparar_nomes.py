import pandas as pd
import re
from difflib import SequenceMatcher

def limpar_nome(nome):
    """Limpa e padroniza nomes para comparação"""
    if pd.isna(nome):
        return ""
    
    nome_str = str(nome).upper().strip()
    
    # Remover caracteres especiais comuns
    nome_str = re.sub(r'[^\w\s]', ' ', nome_str)
    
    # Remover palavras comuns que podem variar
    palavras_remover = ['LTDA', 'LTD', 'S/A', 'SA', 'EIRELI', 'EPP', 'ME', 'SOCIEDADE', 'CIA', 'COMPANHIA']
    for palavra in palavras_remover:
        nome_str = re.sub(rf'\b{palavra}\b', '', nome_str)
    
    # Remover espaços extras
    nome_str = re.sub(r'\s+', ' ', nome_str).strip()
    
    return nome_str

def similaridade(nome1, nome2):
    """Calcula similaridade entre dois nomes"""
    return SequenceMatcher(None, nome1, nome2).ratio()

def analisar_nomes():
    """Analisa os nomes nos dois arquivos"""
    print("ANÁLISE DE NOMES - BOLETOS vs FATURAS")
    print("="*60)
    
    # Carregar boletos
    boletos = pd.read_excel("data/boletos_legalone.xlsx")
    boletos.columns = ['data_recebimento', 'valor_liquido', 'nome_cliente', 'cpf_cnpj']
    
    # Carregar faturas
    faturas = pd.read_excel("data/faturas_emitidas.xlsx")
    faturas.columns = ['status', 'data_emissao', 'numero_fatura', 'agrupado_por', 'destinatario', 'tipo', 'valor']
    
    print(f"Boletos: {len(boletos):,} registros")
    print(f"Faturas: {len(faturas):,} registros")
    
    # Analisar nomes únicos nos boletos
    nomes_boletos = boletos['nome_cliente'].dropna().unique()
    print(f"\nNomes únicos em boletos: {len(nomes_boletos):,}")
    
    # Analisar nomes únicos nas faturas
    nomes_faturas = faturas['destinatario'].dropna().unique()
    print(f"Nomes únicos em faturas: {len(nomes_faturas):,}")
    
    print(f"\nPrimeiros 10 nomes de boletos:")
    for i, nome in enumerate(nomes_boletos[:10], 1):
        print(f"{i:2d}. {nome}")
    
    print(f"\nPrimeiros 10 nomes de faturas:")
    for i, nome in enumerate(nomes_faturas[:10], 1):
        print(f"{i:2d}. {nome}")
    
    # Tentar encontrar correspondências exatas
    print(f"\n{'='*40}")
    print("BUSCANDO CORRESPONDÊNCIAS EXATAS")
    print("="*40)
    
    nomes_boletos_set = set(nomes_boletos)
    nomes_faturas_set = set(nomes_faturas)
    
    correspondencias_exatas = nomes_boletos_set.intersection(nomes_faturas_set)
    print(f"Correspondências exatas: {len(correspondencias_exatas)}")
    
    if len(correspondencias_exatas) > 0:
        print(f"Exemplos de correspondências exatas:")
        for i, nome in enumerate(list(correspondencias_exatas)[:10], 1):
            print(f"{i:2d}. {nome}")
    
    # Tentar correspondências com nomes limpos
    print(f"\n{'='*40}")
    print("BUSCANDO CORRESPONDÊNCIAS COM NOMES LIMPOS")
    print("="*40)
    
    # Limpar nomes
    nomes_boletos_limpos = {limpar_nome(nome): nome for nome in nomes_boletos}
    nomes_faturas_limpos = {limpar_nome(nome): nome for nome in nomes_faturas}
    
    correspondencias_limpas = set(nomes_boletos_limpos.keys()).intersection(set(nomes_faturas_limpos.keys()))
    correspondencias_limpas = {nome for nome in correspondencias_limpas if nome.strip() != ""}
    
    print(f"Correspondências com nomes limpos: {len(correspondencias_limpas)}")
    
    if len(correspondencias_limpas) > 0:
        print(f"Exemplos de correspondências limpas:")
        for i, nome_limpo in enumerate(list(correspondencias_limpas)[:10], 1):
            nome_original_boleto = nomes_boletos_limpos[nome_limpo]
            nome_original_fatura = nomes_faturas_limpos[nome_limpo]
            print(f"{i:2d}. Limpo: '{nome_limpo}'")
            print(f"    Boleto: '{nome_original_boleto}'")
            print(f"    Fatura: '{nome_original_fatura}'")
    
    # Buscar correspondências por similaridade
    print(f"\n{'='*40}")
    print("BUSCANDO CORRESPONDÊNCIAS POR SIMILARIDADE")
    print("="*40)
    
    correspondencias_similares = []
    
    # Para cada nome de fatura, encontrar o mais similar nos boletos
    for nome_fatura in nomes_faturas[:20]:  # Limitar para não demorar muito
        nome_fatura_limpo = limpar_nome(nome_fatura)
        if nome_fatura_limpo.strip() == "":
            continue
            
        melhor_similaridade = 0
        melhor_nome_boleto = ""
        
        for nome_boleto in nomes_boletos:
            nome_boleto_limpo = limpar_nome(nome_boleto)
            if nome_boleto_limpo.strip() == "":
                continue
                
            sim = similaridade(nome_fatura_limpo, nome_boleto_limpo)
            if sim > melhor_similaridade:
                melhor_similaridade = sim
                melhor_nome_boleto = nome_boleto
        
        if melhor_similaridade > 0.7:  # Similaridade alta
            correspondencias_similares.append({
                'nome_fatura': nome_fatura,
                'nome_boleto': melhor_nome_boleto,
                'similaridade': melhor_similaridade
            })
    
    print(f"Correspondências por similaridade (>70%): {len(correspondencias_similares)}")
    
    if len(correspondencias_similares) > 0:
        print(f"Exemplos de correspondências similares:")
        for i, corresp in enumerate(correspondencias_similares[:10], 1):
            print(f"{i:2d}. Similaridade: {corresp['similaridade']:.1%}")
            print(f"    Fatura: '{corresp['nome_fatura']}'")
            print(f"    Boleto: '{corresp['nome_boleto']}'")
    
    # Criar mapeamento de nomes para CPF/CNPJ
    print(f"\n{'='*40}")
    print("CRIANDO MAPEAMENTO NOME -> CPF/CNPJ")
    print("="*40)
    
    # Criar dicionário nome -> CPF/CNPJ dos boletos
    mapeamento_nome_cpf = {}
    
    for _, row in boletos.iterrows():
        nome = row['nome_cliente']
        cpf_cnpj = row['cpf_cnpj']
        
        if pd.notna(nome) and pd.notna(cpf_cnpj):
            nome_limpo = limpar_nome(nome)
            if nome_limpo.strip() != "":
                if nome_limpo not in mapeamento_nome_cpf:
                    mapeamento_nome_cpf[nome_limpo] = set()
                mapeamento_nome_cpf[nome_limpo].add(str(cpf_cnpj).strip())
    
    print(f"Nomes mapeados para CPF/CNPJ: {len(mapeamento_nome_cpf)}")
    
    # Tentar mapear faturas para CPF/CNPJ
    faturas_mapeadas = 0
    
    for nome_fatura in nomes_faturas:
        nome_fatura_limpo = limpar_nome(nome_fatura)
        if nome_fatura_limpo in mapeamento_nome_cpf:
            faturas_mapeadas += 1
    
    print(f"Nomes de faturas que podem ser mapeados: {faturas_mapeadas}")
    
    # Mostrar alguns exemplos de mapeamento
    print(f"\nExemplos de mapeamento:")
    count = 0
    for nome_limpo, cpfs in mapeamento_nome_cpf.items():
        if count >= 10:
            break
        if nome_limpo in [limpar_nome(nf) for nf in nomes_faturas]:
            print(f"Nome: '{nome_limpo}' -> CPF/CNPJ: {list(cpfs)}")
            count += 1

def main():
    analisar_nomes()

if __name__ == "__main__":
    main()
