import pandas as pd
import re

def examinar_faturas_detalhado():
    """Examina os dados das faturas em detalhes"""
    print("EXAME DETALHADO DAS FATURAS")
    print("="*50)
    
    # Carregar dados
    faturas = pd.read_excel("data/faturas_emitidas.xlsx")
    print(f"Total de registros: {len(faturas):,}")
    
    # Mostrar estrutura
    print(f"\nColunas:")
    for i, col in enumerate(faturas.columns, 1):
        print(f"{i}. {col}")
    
    # Examinar campo "Destinatário da fatura" em detalhes
    print(f"\nEXAME DO CAMPO 'Destinatário da fatura':")
    destinatarios = faturas['Destinatário da fatura'].dropna()
    print(f"Total de destinatários não nulos: {len(destinatarios)}")
    print(f"Destinatários únicos: {destinatarios.nunique()}")
    
    print(f"\nPrimeiros 20 exemplos de destinatários:")
    for i, dest in enumerate(destinatarios.head(20), 1):
        print(f"{i:2d}. {dest}")
    
    # Procurar padrões de CPF/CNPJ
    print(f"\nPROCURANDO PADRÕES DE CPF/CNPJ:")
    
    # Padrão CPF: xxx.xxx.xxx-xx
    padrao_cpf = r'\d{3}\.\d{3}\.\d{3}-\d{2}'
    cpfs_encontrados = destinatarios.str.extract(f'({padrao_cpf})', expand=False).dropna()
    print(f"CPFs encontrados (formato xxx.xxx.xxx-xx): {len(cpfs_encontrados)}")
    if len(cpfs_encontrados) > 0:
        print(f"Exemplos: {cpfs_encontrados.head(5).tolist()}")
    
    # Padrão CNPJ: xx.xxx.xxx/xxxx-xx
    padrao_cnpj = r'\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2}'
    cnpjs_encontrados = destinatarios.str.extract(f'({padrao_cnpj})', expand=False).dropna()
    print(f"CNPJs encontrados (formato xx.xxx.xxx/xxxx-xx): {len(cnpjs_encontrados)}")
    if len(cnpjs_encontrados) > 0:
        print(f"Exemplos: {cnpjs_encontrados.head(5).tolist()}")
    
    # Procurar sequências de números
    print(f"\nPROCURANDO SEQUÊNCIAS DE NÚMEROS:")
    def extrair_numeros(texto):
        if pd.isna(texto):
            return []
        numeros = re.findall(r'\d+', str(texto))
        return [n for n in numeros if len(n) >= 10]  # Pelo menos 10 dígitos
    
    numeros_longos = destinatarios.apply(extrair_numeros)
    numeros_encontrados = [num for lista in numeros_longos for num in lista]
    
    print(f"Sequências de números com 10+ dígitos: {len(numeros_encontrados)}")
    if len(numeros_encontrados) > 0:
        print(f"Exemplos: {numeros_encontrados[:10]}")
        
        # Verificar tamanhos
        tamanhos = {}
        for num in numeros_encontrados:
            tam = len(num)
            tamanhos[tam] = tamanhos.get(tam, 0) + 1
        
        print(f"Distribuição por tamanho:")
        for tam, qtd in sorted(tamanhos.items()):
            print(f"  {tam} dígitos: {qtd} ocorrências")
    
    # Examinar campo "Agrupado por"
    print(f"\nEXAME DO CAMPO 'Agrupado por':")
    agrupados = faturas['Agrupado por'].dropna()
    print(f"Total não nulos: {len(agrupados)}")
    print(f"Valores únicos: {agrupados.nunique()}")
    
    print(f"\nPrimeiros 10 exemplos de 'Agrupado por':")
    for i, agrup in enumerate(agrupados.head(10), 1):
        print(f"{i:2d}. {agrup}")
    
    # Verificar se há CPF/CNPJ em "Agrupado por"
    cpfs_agrupado = agrupados.str.extract(f'({padrao_cpf})', expand=False).dropna()
    cnpjs_agrupado = agrupados.str.extract(f'({padrao_cnpj})', expand=False).dropna()
    
    print(f"\nCPFs em 'Agrupado por': {len(cpfs_agrupado)}")
    print(f"CNPJs em 'Agrupado por': {len(cnpjs_agrupado)}")
    
    # Mostrar alguns registros completos
    print(f"\nREGISTROS COMPLETOS (primeiros 5):")
    for i, (idx, row) in enumerate(faturas.head(5).iterrows(), 1):
        print(f"\nRegistro {i}:")
        for col in faturas.columns:
            print(f"  {col}: {row[col]}")

def main():
    examinar_faturas_detalhado()

if __name__ == "__main__":
    main()
