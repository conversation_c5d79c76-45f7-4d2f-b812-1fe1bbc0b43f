import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def limpar_cpf_cnpj(cpf_cnpj):
    """Limpa e padroniza CPF/CNPJ"""
    if pd.isna(cpf_cnpj):
        return None
    
    # Converter para string e remover caracteres especiais
    cpf_cnpj_str = str(cpf_cnpj).strip()
    cpf_cnpj_limpo = re.sub(r'[^\d]', '', cpf_cnpj_str)
    
    # Verificar se tem tamanho válido
    if len(cpf_cnpj_limpo) == 11:  # CPF
        return cpf_cnpj_limpo
    elif len(cpf_cnpj_limpo) == 14:  # CNPJ
        return cpf_cnpj_limpo
    else:
        return None

def limpar_nome(nome):
    """Limpa e normaliza nomes para matching"""
    if pd.isna(nome):
        return ""
    
    nome_str = str(nome).upper().strip()
    
    # Remover termos comuns de empresas
    termos_remover = [
        'LTDA', 'LIMITADA', 'S/A', 'S.A.', 'SA', 'SOCIEDADE ANONIMA',
        'EIRELI', 'ME', 'EPP', 'MICROEMPRESA', 'EMPRESA INDIVIDUAL',
        'CIA', 'COMPANHIA', '&', 'E', 'SERVICOS', 'COMERCIO',
        'INDUSTRIA', 'CONSULTORIA', 'ASSESSORIA'
    ]
    
    for termo in termos_remover:
        nome_str = re.sub(r'\b' + termo + r'\b', '', nome_str)
    
    # Remover caracteres especiais e espaços extras
    nome_str = re.sub(r'[^\w\s]', ' ', nome_str)
    nome_str = re.sub(r'\s+', ' ', nome_str).strip()
    
    return nome_str

def carregar_dados():
    """Carrega todos os dados necessários"""
    print("Carregando dados...")
    
    # Carregar boletos
    print("- Carregando boletos...")
    df_boletos = pd.read_excel("data/boletos_legalone.xlsx")
    print(f"  ✓ {len(df_boletos)} registros de boletos")
    
    # Carregar base PF
    print("- Carregando base PF...")
    df_pf = pd.read_excel("data/base_pf_legalone.xlsx")
    print(f"  ✓ {len(df_pf)} registros PF")
    
    # Carregar base PJ
    print("- Carregando base PJ...")
    df_pj = pd.read_excel("data/base_pj_legalone.xlsx")
    print(f"  ✓ {len(df_pj)} registros PJ")
    
    # Carregar CNAEs
    print("- Carregando CNAEs...")
    df_cnaes = pd.read_excel("data/cnaes.xlsx")
    print(f"  ✓ {len(df_cnaes)} registros de CNAE")
    
    return df_boletos, df_pf, df_pj, df_cnaes

def processar_boletos(df_boletos):
    """Processa dados dos boletos"""
    print("\nProcessando boletos...")
    
    # Renomear colunas para facilitar
    df_boletos = df_boletos.rename(columns={
        'Movimentos / Recebido em': 'data_recebimento',
        'Valor líquido realizado': 'valor',
        'Classificações / Destinatário da fatura / Nome/razão social': 'nome',
        'Classificações / Destinatário da fatura / CPF/CNPJ': 'cpf_cnpj'
    })
    
    # Limpar CPF/CNPJ
    df_boletos['cpf_cnpj_limpo'] = df_boletos['cpf_cnpj'].apply(limpar_cpf_cnpj)
    df_boletos['nome_limpo'] = df_boletos['nome'].apply(limpar_nome)
    
    # Remover registros sem CPF/CNPJ válido
    df_boletos_validos = df_boletos[df_boletos['cpf_cnpj_limpo'].notna()].copy()
    
    print(f"  ✓ {len(df_boletos_validos)} boletos com CPF/CNPJ válido processados")
    
    return df_boletos_validos

def processar_bases_clientes(df_pf, df_pj):
    """Processa bases de clientes PF e PJ com extração melhorada"""
    print("\nProcessando bases de clientes...")
    
    # Processar PF
    df_pf_proc = df_pf.copy()
    df_pf_proc['cpf_cnpj_limpo'] = df_pf_proc['CPF'].apply(limpar_cpf_cnpj)
    df_pf_proc['tipo'] = 'PF'
    
    # Mapear colunas PF (usando as colunas corretas identificadas)
    df_pf_proc = df_pf_proc.rename(columns={
        'Nome': 'nome',
        'Endereços / UF': 'uf'
    })
    
    # Consolidar telefones PF (usar ambas as colunas)
    df_pf_proc['telefone_principal'] = df_pf_proc['Telefones / Principal']
    df_pf_proc['telefone_numero'] = df_pf_proc['Telefones / Número']
    
    # Consolidar emails PF (usar a coluna principal)
    df_pf_proc['email'] = df_pf_proc['E-mails / E-mail']
    
    # Processar PJ
    df_pj_proc = df_pj.copy()
    df_pj_proc['cpf_cnpj_limpo'] = df_pj_proc['CNPJ'].apply(limpar_cpf_cnpj)
    df_pj_proc['tipo'] = 'PJ'
    
    # Mapear colunas PJ (usando as colunas corretas identificadas)
    df_pj_proc = df_pj_proc.rename(columns={
        'Razão social': 'nome',
        'Endereços / UF': 'uf',  # Esta é a coluna principal de UF (69.5% preenchida)
        'CNAEs / Código': 'cnae',
        'Regime Tributário': 'regime_tributario'
    })
    
    # Consolidar telefones PJ (usar ambas as colunas)
    df_pj_proc['telefone_principal'] = df_pj_proc['Telefones / Principal']
    df_pj_proc['telefone_numero'] = df_pj_proc['Telefones / Número']
    
    # Consolidar emails PJ (usar a coluna principal)
    df_pj_proc['email'] = df_pj_proc['E-mails / E-mail']
    
    # Combinar bases
    colunas_comuns = ['nome', 'cpf_cnpj_limpo', 'tipo', 'uf', 'email', 'telefone_principal', 'telefone_numero']
    colunas_pj_extras = ['cnae', 'regime_tributario']
    
    # Adicionar colunas extras para PF (preenchidas com NaN)
    for col in colunas_pj_extras:
        df_pf_proc[col] = np.nan
    
    # Selecionar colunas
    df_pf_final = df_pf_proc[colunas_comuns + colunas_pj_extras]
    df_pj_final = df_pj_proc[colunas_comuns + colunas_pj_extras]
    
    # Combinar
    df_clientes = pd.concat([df_pf_final, df_pj_final], ignore_index=True)
    
    # Remover duplicatas e registros sem CPF/CNPJ
    df_clientes = df_clientes[df_clientes['cpf_cnpj_limpo'].notna()]
    df_clientes = df_clientes.drop_duplicates(subset=['cpf_cnpj_limpo'])
    
    print(f"  ✓ {len(df_clientes)} clientes únicos processados")
    
    return df_clientes

def calcular_metricas_boletos(df_boletos_validos):
    """Calcula métricas dos boletos por cliente"""
    print("\nCalculando métricas dos boletos...")
    
    # Agrupar por CPF/CNPJ
    metricas = []
    
    for cpf_cnpj, grupo in df_boletos_validos.groupby('cpf_cnpj_limpo'):
        # Ordenar por data
        grupo_ordenado = grupo.sort_values('data_recebimento')
        
        # Calcular métricas
        data_primeiro_boleto = grupo_ordenado['data_recebimento'].min()
        data_ultimo_boleto = grupo_ordenado['data_recebimento'].max()
        
        # Calcular meses desde último boleto
        hoje = datetime.now()
        meses_desde_ultimo = ((hoje.year - data_ultimo_boleto.year) * 12 + 
                             (hoje.month - data_ultimo_boleto.month))
        
        # Calcular período cliente em meses (adicionar 1 conforme preferência do usuário)
        periodo_meses = ((data_ultimo_boleto.year - data_primeiro_boleto.year) * 12 + 
                        (data_ultimo_boleto.month - data_primeiro_boleto.month)) + 1
        
        # Métricas de valor
        total_boletos = len(grupo)
        valor_total = grupo['valor'].sum()
        valor_medio = grupo['valor'].mean()
        valor_minimo = grupo['valor'].min()
        valor_maximo = grupo['valor'].max()
        
        # Frequência (boletos por mês)
        frequencia_por_mes = total_boletos / max(periodo_meses, 1)
        
        # Nome (pegar o mais comum)
        nome_mais_comum = grupo['nome'].mode().iloc[0] if not grupo['nome'].mode().empty else grupo['nome'].iloc[0]
        
        metricas.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome': nome_mais_comum,
            'data_primeiro_boleto': data_primeiro_boleto,
            'data_ultimo_boleto': data_ultimo_boleto,
            'meses_desde_ultimo_boleto': meses_desde_ultimo,
            'periodo_cliente_meses': periodo_meses,
            'total_boletos': total_boletos,
            'valor_total': valor_total,
            'valor_medio': valor_medio,
            'valor_minimo': valor_minimo,
            'valor_maximo': valor_maximo,
            'frequencia_por_mes': frequencia_por_mes
        })
    
    df_metricas = pd.DataFrame(metricas)
    print(f"  ✓ Métricas calculadas para {len(df_metricas)} clientes")
    
    return df_metricas

def consolidar_dados_cliente(df_clientes):
    """Consolida dados de clientes com extração melhorada"""
    print("\nConsolidando dados de clientes...")
    
    # Agrupar por CPF/CNPJ e consolidar informações
    clientes_consolidados = []
    
    for cpf_cnpj, grupo in df_clientes.groupby('cpf_cnpj_limpo'):
        # Pegar primeiro registro não-nulo para cada campo
        nome = grupo['nome'].dropna().iloc[0] if not grupo['nome'].dropna().empty else None
        tipo = grupo['tipo'].dropna().iloc[0] if not grupo['tipo'].dropna().empty else None
        uf = grupo['uf'].dropna().iloc[0] if not grupo['uf'].dropna().empty else None
        regime_tributario = grupo['regime_tributario'].dropna().iloc[0] if not grupo['regime_tributario'].dropna().empty else None
        
        # Consolidar telefones (usar ambas as colunas e remover duplicatas)
        telefones_lista = []
        for col in ['telefone_principal', 'telefone_numero']:
            telefones = grupo[col].dropna().unique()
            for tel in telefones:
                tel_str = str(tel).strip()
                if tel_str not in ['nan', 'None', 'Sim', 'Não', ''] and len(tel_str) > 3:
                    telefones_lista.append(tel_str)
        
        telefones_unicos = list(set(telefones_lista))  # Remover duplicatas
        telefones_str = '; '.join(telefones_unicos) if telefones_unicos else None
        
        # Consolidar emails (remover duplicatas e valores inválidos)
        emails = grupo['email'].dropna().unique()
        emails_validos = []
        for email in emails:
            email_str = str(email).strip()
            if email_str not in ['nan', 'None', 'Sim', 'Não', ''] and '@' in email_str:
                emails_validos.append(email_str)
        
        emails_str = '; '.join(list(set(emails_validos))) if emails_validos else None
        
        # Consolidar CNAEs
        cnaes = grupo['cnae'].dropna().unique()
        cnae_principal = None
        cnaes_str = None
        
        if len(cnaes) > 0:
            cnaes_validos = []
            for cnae in cnaes:
                cnae_str = str(cnae).strip()
                if cnae_str not in ['nan', 'None', '']:
                    cnaes_validos.append(cnae_str)
            
            if cnaes_validos:
                cnae_principal = cnaes_validos[0]
                cnaes_str = '; '.join(list(set(cnaes_validos)))
        
        clientes_consolidados.append({
            'cpf_cnpj_limpo': cpf_cnpj,
            'nome_cliente': nome,
            'tipo_destinatario': tipo,
            'uf': uf,
            'telefones': telefones_str,
            'emails': emails_str,
            'cnae': cnaes_str,
            'primeiro_cnae': cnae_principal,
            'regime_tributario': regime_tributario
        })
    
    df_consolidado = pd.DataFrame(clientes_consolidados)
    print(f"  ✓ {len(df_consolidado)} clientes consolidados")

    return df_consolidado

def adicionar_descricao_cnae(df_final, df_cnaes):
    """Adiciona descrição do CNAE baseado no primeiro_cnae"""
    print("\nAdicionando descrições de CNAE...")

    # Preparar dicionário de CNAEs
    cnae_dict = {}
    for _, row in df_cnaes.iterrows():
        cnae_codigo = str(row['CNAE']).strip()
        cnae_descricao = str(row['DESCRIÇÃO']).strip()
        cnae_dict[cnae_codigo] = cnae_descricao

    # Função para buscar descrição
    def buscar_descricao_cnae(cnae_codigo):
        if pd.isna(cnae_codigo) or str(cnae_codigo).strip() in ['nan', 'None', '']:
            return None

        cnae_str = str(cnae_codigo).strip()

        # Tentar busca exata primeiro
        if cnae_str in cnae_dict:
            return cnae_dict[cnae_str]

        # Tentar busca sem formatação (remover pontos e barras)
        cnae_limpo = re.sub(r'[^\d]', '', cnae_str)
        for codigo, descricao in cnae_dict.items():
            codigo_limpo = re.sub(r'[^\d]', '', codigo)
            if cnae_limpo == codigo_limpo:
                return descricao

        # Tentar busca por início (para CNAEs incompletos)
        for codigo, descricao in cnae_dict.items():
            if codigo.startswith(cnae_str) or cnae_str.startswith(codigo):
                return descricao

        return None

    # Aplicar busca de descrição
    df_final['Descricao_CNAE'] = df_final['Primeiro_CNAE'].apply(buscar_descricao_cnae)

    # Estatísticas
    total_cnaes = df_final['Primeiro_CNAE'].notna().sum()
    cnaes_com_descricao = df_final['Descricao_CNAE'].notna().sum()

    print(f"  ✓ {cnaes_com_descricao}/{total_cnaes} CNAEs com descrição encontrada ({cnaes_com_descricao/total_cnaes*100:.1f}%)")

    return df_final

def formatar_datas_dd_mm_aaaa(df_formatado):
    """Formata as datas para DD/MM/AAAA"""
    print("\nFormatando datas para DD/MM/AAAA...")

    colunas_data = ['Data_Ultimo_Boleto', 'Data_Primeiro_Boleto']

    for coluna in colunas_data:
        if coluna in df_formatado.columns:
            # Converter para datetime se não estiver
            df_formatado[coluna] = pd.to_datetime(df_formatado[coluna], errors='coerce')

            # Formatar como DD/MM/AAAA
            df_formatado[coluna] = df_formatado[coluna].dt.strftime('%d/%m/%Y')

            # Substituir NaT por None
            df_formatado[coluna] = df_formatado[coluna].replace('NaT', None)

    print(f"  ✓ Datas formatadas para DD/MM/AAAA")

    return df_formatado

def formatar_arquivo_final(df_metricas, df_clientes_consolidado, df_cnaes):
    """Formata dados no formato correto com nomes de BOLETOS"""
    print("\nFormatando arquivo final com nomes corretos de BOLETOS...")

    # Fazer merge
    df_final = df_metricas.merge(df_clientes_consolidado, on='cpf_cnpj_limpo', how='left')

    # Criar DataFrame com nomes CORRETOS (BOLETOS, não faturas)
    df_formatado = pd.DataFrame()

    # Mapear colunas com nomes corretos de BOLETOS
    df_formatado['Nome'] = df_final['nome_cliente'].fillna(df_final['nome'])

    # Converter CPF/CNPJ para float (formato do arquivo original)
    try:
        df_formatado['CPF_CNPJ'] = pd.to_numeric(df_final['cpf_cnpj_limpo'], errors='coerce')
    except:
        df_formatado['CPF_CNPJ'] = df_final['cpf_cnpj_limpo']

    df_formatado['Tipo_Destinatario'] = df_final['tipo_destinatario']
    df_formatado['Meses_Desde_Ultimo_Boleto'] = df_final['meses_desde_ultimo_boleto']  # CORRIGIDO: Boleto
    df_formatado['Data_Ultimo_Boleto'] = df_final['data_ultimo_boleto']  # CORRIGIDO: Boleto
    df_formatado['Data_Primeiro_Boleto'] = df_final['data_primeiro_boleto']  # CORRIGIDO: Boleto
    df_formatado['Periodo_Cliente_Meses'] = df_final['periodo_cliente_meses']
    df_formatado['Total_Boletos_Unicos'] = df_final['total_boletos']  # CORRIGIDO: Boletos
    df_formatado['Valor_Total_Recebido'] = df_final['valor_total'].round(2)  # CORRIGIDO: Recebido (via boletos)
    df_formatado['Valor_Medio_Por_Boleto'] = df_final['valor_medio'].round(2)  # CORRIGIDO: Por Boleto
    df_formatado['Valor_Minimo'] = df_final['valor_minimo'].round(2)
    df_formatado['Valor_Maximo'] = df_final['valor_maximo'].round(2)
    df_formatado['Frequencia_Boletos_Por_Mes'] = df_final['frequencia_por_mes'].round(2)  # CORRIGIDO: Boletos
    df_formatado['Telefones'] = df_final['telefones']
    df_formatado['Emails'] = df_final['emails']
    df_formatado['UF'] = df_final['uf']
    df_formatado['CNAE'] = df_final['cnae']
    df_formatado['Regime_Tributario'] = df_final['regime_tributario']
    df_formatado['Primeiro_CNAE'] = df_final['primeiro_cnae']
    df_formatado['Descricao_CNAE'] = np.nan  # Será preenchido pela função adicionar_descricao_cnae

    # Adicionar descrição do CNAE
    df_formatado = adicionar_descricao_cnae(df_formatado, df_cnaes)

    # Formatar datas
    df_formatado = formatar_datas_dd_mm_aaaa(df_formatado)

    print(f"  ✓ Arquivo formatado com {len(df_formatado)} registros")
    print(f"  ✓ Nomes das colunas corrigidos para refletir dados de BOLETOS")

    return df_formatado, df_final

def salvar_arquivo(df_formatado, df_detalhado):
    """Salva o arquivo final com nomes corretos"""
    print("\nSalvando arquivo...")

    nome_arquivo = "teste_novo/arquivo_final_boletos_nomes_corretos.xlsx"
    df_formatado.to_excel(nome_arquivo, index=False)

    print(f"  ✓ Arquivo salvo: {nome_arquivo}")

    # Salvar também versão com detalhes adicionais
    nome_arquivo_detalhado = "teste_novo/arquivo_final_boletos_detalhado_nomes_corretos.xlsx"
    df_detalhado.to_excel(nome_arquivo_detalhado, index=False)
    print(f"  ✓ Arquivo detalhado salvo: {nome_arquivo_detalhado}")

    # Mostrar resumo
    print(f"\nResumo do arquivo gerado:")
    print(f"- Total de registros: {len(df_formatado)}")
    print(f"- Clientes PF: {len(df_formatado[df_formatado['Tipo_Destinatario'] == 'PF'])}")
    print(f"- Clientes PJ: {len(df_formatado[df_formatado['Tipo_Destinatario'] == 'PJ'])}")
    print(f"- Valor total recebido: R$ {df_formatado['Valor_Total_Recebido'].sum():,.2f}")
    print(f"- Clientes com telefone: {df_formatado['Telefones'].notna().sum()}")
    print(f"- Clientes com email: {df_formatado['Emails'].notna().sum()}")
    print(f"- Clientes com CNAE: {df_formatado['CNAE'].notna().sum()}")
    print(f"- CNAEs com descrição: {df_formatado['Descricao_CNAE'].notna().sum()}")

    # Mostrar nomes das colunas
    print(f"\nColunas do arquivo (com nomes corretos de BOLETOS):")
    for i, col in enumerate(df_formatado.columns, 1):
        print(f"  {i:2d}. {col}")

def main():
    print("CRIANDO ARQUIVO FINAL COM NOMES CORRETOS DE BOLETOS")
    print("="*60)

    # Carregar dados
    df_boletos, df_pf, df_pj, df_cnaes = carregar_dados()

    # Processar boletos
    df_boletos_proc = processar_boletos(df_boletos)

    # Processar bases de clientes
    df_clientes = processar_bases_clientes(df_pf, df_pj)

    # Consolidar dados de clientes
    df_clientes_consolidado = consolidar_dados_cliente(df_clientes)

    # Calcular métricas dos boletos
    df_metricas = calcular_metricas_boletos(df_boletos_proc)

    # Formatar arquivo final
    df_formatado, df_detalhado = formatar_arquivo_final(df_metricas, df_clientes_consolidado, df_cnaes)

    # Salvar arquivo
    salvar_arquivo(df_formatado, df_detalhado)

    return df_formatado, df_detalhado

if __name__ == "__main__":
    df_resultado, df_detalhado = main()
